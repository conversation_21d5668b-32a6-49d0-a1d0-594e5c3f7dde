"""
测试优化后的液口距校准接口
验证数据通过call传入predict方法的逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel

def test_complete_data_mode():
    """测试完整数据模式"""
    print("=== 测试完整数据模式 ===")
    
    model = YekoujuAdjustModel()
    
    # 模拟完整的数据传入
    data = {
        # 上一炉数据
        "last_auto_ccd": 1450.0,      # 上次自动定埚位完成后CCD温度
        "last_auto_liquid": 25.0,     # 上次自动定埚位完成后绝对液口距
        "last_yinjing_target": 1448.0, # 上次引晶前目标温度
        "last_yinjing_liquid": 26.0,  # 上次引晶前绝对液口距
        
        # 本次数据
        "current_auto_ccd": 1452.0,   # 本次自动定埚位完成后CCD温度
        "target_temp": 1449.0,        # 本次引晶前目标温度
        
        # 控制参数
        "manual_adjusted": False,     # 未人工调整
        
        # 传统参数（用于回退，可选）
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    result = model.predict(data)
    print(f"完整数据模式预测结果: {result}mm")
    
    # 计算过程验证
    delta_t_last = data["last_yinjing_target"] - data["last_auto_ccd"]  # -2.0
    delta_l_last = data["last_yinjing_liquid"] - data["last_auto_liquid"]  # 1.0
    r = delta_l_last / delta_t_last  # -0.5
    delta_t_current = data["target_temp"] - data["current_auto_ccd"]  # -3.0
    expected_correction = r * delta_t_current  # 1.5
    
    print(f"计算验证:")
    print(f"  上次温度偏差: {delta_t_last}°C")
    print(f"  上次液口距变化: {delta_l_last}mm")
    print(f"  比例系数: {r} mm/°C")
    print(f"  本次温度偏差: {delta_t_current}°C")
    print(f"  预期修正量: {expected_correction}mm")
    print(f"  实际结果: {result}mm")
    print(f"  结果匹配: {'✅' if abs(result - expected_correction) < 0.01 else '❌'}")

def test_manual_adjusted_mode():
    """测试人工调整模式"""
    print("\n=== 测试人工调整模式 ===")
    
    model = YekoujuAdjustModel()
    
    data = {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1448.0,
        "last_yinjing_liquid": 26.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": True,  # 人工调整过
        
        # 传统参数
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    result = model.predict(data)
    print(f"人工调整模式预测结果: {result}mm")
    print(f"应该回退到传统模型: {'✅' if result == 0.0 else '❌'}")

def test_incomplete_data_mode():
    """测试数据不完整模式"""
    print("\n=== 测试数据不完整模式 ===")
    
    model = YekoujuAdjustModel()
    
    # 缺少历史数据
    data = {
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        
        # 传统参数
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    result = model.predict(data)
    print(f"数据不完整模式预测结果: {result}mm")
    print(f"应该回退到传统模型: {'✅' if result == 0.0 else '❌'}")

def test_traditional_mode():
    """测试纯传统模式"""
    print("\n=== 测试纯传统模式 ===")
    
    model = YekoujuAdjustModel()
    
    # 只有传统参数
    data = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    result = model.predict(data)
    print(f"纯传统模式预测结果: {result}mm")
    print(f"使用传统模型: {'✅' if result == 0.0 else '❌'}")  # 因为没有加载sklearn模型

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    model = YekoujuAdjustModel()
    
    # 温度变化很小的情况
    data1 = {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1450.05,  # 温度变化很小
        "last_yinjing_liquid": 26.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False
    }
    
    result1 = model.predict(data1)
    print(f"温度变化很小情况: {result1}mm")
    
    # 极端温度偏差
    data2 = {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1400.0,  # 极端温度差
        "last_yinjing_liquid": 30.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False
    }
    
    result2 = model.predict(data2)
    print(f"极端温度偏差情况: {result2}mm (应被限制在合理范围)")

def test_api_simulation():
    """模拟API调用"""
    print("\n=== 模拟API调用 ===")
    
    # 模拟request.json数据
    class MockRequest:
        def __init__(self, json_data):
            self.json = json_data
            self.method = 'POST'
    
    # 完整数据API调用
    api_data = {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1448.0,
        "last_yinjing_liquid": 26.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False,
        
        # 传统参数
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0,
        "recent_absolute_liquid": [25.1, 25.0, 24.9, 25.2, 25.0]
    }
    
    mock_request = MockRequest(api_data)
    
    # 这里应该调用call.py中的yekoujuadjust函数
    # 但为了简化测试，直接使用模型
    model = YekoujuAdjustModel()
    
    # 构建与call.py相同的数据结构
    data = {
        "last_auto_ccd": api_data["last_auto_ccd"],
        "last_auto_liquid": api_data["last_auto_liquid"],
        "last_yinjing_target": api_data["last_yinjing_target"],
        "last_yinjing_liquid": api_data["last_yinjing_liquid"],
        "current_auto_ccd": api_data["current_auto_ccd"],
        "target_temp": api_data["target_temp"],
        "manual_adjusted": api_data["manual_adjusted"],
        "daoliutong_up": api_data["daoliutong_up"],
        "daoliutong_down": api_data["daoliutong_down"],
        "daoliutong_left": api_data["daoliutong_left"],
        "daoliutong_right": api_data["daoliutong_right"],
        "daoliutong_upleft": api_data["daoliutong_upleft"],
        "daoliutong_upright": api_data["daoliutong_upright"],
        "dingguo_finish_yewen": api_data["dingguo_finish_yewen"],
        "dingguo_finish_guowei": api_data["dingguo_finish_guowei"],
        "dingguo_finish_yekouju": api_data["dingguo_finish_yekouju"]
    }
    
    result = model.predict(data)
    print(f"API模拟调用结果: {result}mm")

if __name__ == "__main__":
    print("液口距校准接口逻辑测试")
    print("=" * 60)
    
    try:
        test_complete_data_mode()
        test_manual_adjusted_mode()
        test_incomplete_data_mode()
        test_traditional_mode()
        test_edge_cases()
        test_api_simulation()
        
        print("\n" + "=" * 60)
        print("✅ 接口逻辑测试完成")
        print("💡 核心逻辑：所有数据通过call传入predict方法，接口设计正确")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
