"""
液口距校准v3版本测试数据
提供完整的测试数据集，用于验证优化算法和传统模型
"""

# 测试数据集1：正常优化模式
test_data_1 = {
    # 传统模型参数 (x)
    "x": {
        "daoliutong_up": 1.2,
        "daoliutong_down": 0.8,
        "daoliutong_left": 1.1,
        "daoliutong_right": 0.9,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.5,
        "dingguo_finish_guowei": 100.2,
        "dingguo_finish_yekouju": 25.3
    },
    
    # 优化算法参数
    "optimization_params": {
        "last_auto_ccd": 1450.0,           # 上次自动定埚位完成后CCD温度
        "last_auto_liquid": 25.0,          # 上次自动定埚位完成后绝对液口距
        "last_yinjing_target": 1448.0,     # 上次引晶前目标温度
        "last_yinjing_liquid": 26.0,       # 上次引晶前绝对液口距
        "current_auto_ccd": 1452.0,        # 本次自动定埚位完成后CCD温度
        "target_temp": 1449.0,             # 本次引晶前目标温度
        "manual_adjusted": False           # 未人工调整
    },
    
    # 预期结果
    "expected": {
        "delta_t_last": -2.0,              # 上次温度偏差
        "delta_l_last": 1.0,               # 上次液口距变化
        "ratio_r": -0.5,                   # 比例系数
        "delta_t_current": -3.0,           # 本次温度偏差
        "correction": 1.5                  # 预期修正量
    }
}

# 测试数据集2：人工调整模式
test_data_2 = {
    "x": {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1451.0,
        "dingguo_finish_guowei": 99.8,
        "dingguo_finish_yekouju": 24.8
    },
    
    "optimization_params": {
        "last_auto_ccd": 1449.0,
        "last_auto_liquid": 24.5,
        "last_yinjing_target": 1447.0,
        "last_yinjing_liquid": 25.5,
        "current_auto_ccd": 1451.0,
        "target_temp": 1448.0,
        "manual_adjusted": True             # 人工调整过
    },
    
    "expected": {
        "should_skip": True,                # 应该跳过优化算法
        "correction": 0.0                   # 预期返回0
    }
}

# 测试数据集3：温度变化很小
test_data_3 = {
    "x": {
        "daoliutong_up": 0.9,
        "daoliutong_down": 1.1,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 0.95,
        "daoliutong_upright": 1.05,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    },
    
    "optimization_params": {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1450.05,    # 温度变化很小
        "last_yinjing_liquid": 25.5,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False
    },
    
    "expected": {
        "delta_t_last": 0.05,              # 温度变化很小
        "use_default_r": True,             # 应使用默认比例系数
        "default_r": 0.1,                  # 默认比例系数
        "delta_t_current": -3.0,
        "correction": -0.3                 # 0.1 * (-3.0)
    }
}

# 测试数据集4：纯传统模式
test_data_4 = {
    "x": {
        "daoliutong_up": 1.3,
        "daoliutong_down": 0.7,
        "daoliutong_left": 1.2,
        "daoliutong_right": 0.8,
        "daoliutong_upleft": 1.1,
        "daoliutong_upright": 0.9,
        "dingguo_finish_yewen": 1449.5,
        "dingguo_finish_guowei": 101.0,
        "dingguo_finish_yekouju": 24.5
    },
    
    "optimization_params": None,           # 不传入优化参数
    
    "expected": {
        "use_traditional": True,           # 应使用传统模型
        "correction": 0.0                  # 传统模型不可用时返回0
    }
}

# 测试数据集5：优化参数不完整
test_data_5 = {
    "x": {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    },
    
    "optimization_params": {
        "last_auto_ccd": 1450.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        # 缺少 last_auto_liquid, last_yinjing_target, last_yinjing_liquid
    },
    
    "expected": {
        "incomplete_data": True,           # 数据不完整
        "use_traditional": True,           # 应回退到传统模型
        "correction": 0.0
    }
}

# 测试数据集6：极端值测试
test_data_6 = {
    "x": {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    },
    
    "optimization_params": {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1400.0,     # 极端温度差
        "last_yinjing_liquid": 30.0,       # 极端液口距变化
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False
    },
    
    "expected": {
        "delta_t_last": -50.0,             # 极端温度偏差
        "delta_l_last": 5.0,               # 极端液口距变化
        "ratio_r_raw": -0.1,               # 原始比例系数
        "ratio_r_clipped": -0.1,           # 限制后的比例系数
        "delta_t_current": -3.0,
        "correction_raw": 0.3,             # 原始修正量
        "correction_clipped": 0.3          # 限制后的修正量
    }
}

# API调用测试数据
api_test_data = {
    # 完整的API请求数据
    "request_data": {
        # 优化算法参数
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1448.0,
        "last_yinjing_liquid": 26.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False,
        
        # 传统参数
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    },
    
    # 预期API响应
    "expected_response": {
        "guowei_adjust": 1.5,
        "version": "v3_optimized"
    }
}

# 边界值测试数据
boundary_test_data = [
    {
        "name": "最小修正量",
        "x": {"daoliutong_up": 1.0, "daoliutong_down": 1.0, "daoliutong_left": 1.0, 
              "daoliutong_right": 1.0, "daoliutong_upleft": 1.0, "daoliutong_upright": 1.0,
              "dingguo_finish_yewen": 1450.0, "dingguo_finish_guowei": 100.0, "dingguo_finish_yekouju": 25.0},
        "optimization_params": {
            "last_auto_ccd": 1450.0, "last_auto_liquid": 25.0, "last_yinjing_target": 1448.0,
            "last_yinjing_liquid": 25.1, "current_auto_ccd": 1450.1, "target_temp": 1450.0,
            "manual_adjusted": False
        },
        "expected_range": (-0.1, 0.1)
    },
    {
        "name": "最大修正量",
        "x": {"daoliutong_up": 1.0, "daoliutong_down": 1.0, "daoliutong_left": 1.0,
              "daoliutong_right": 1.0, "daoliutong_upleft": 1.0, "daoliutong_upright": 1.0,
              "dingguo_finish_yewen": 1450.0, "dingguo_finish_guowei": 100.0, "dingguo_finish_yekouju": 25.0},
        "optimization_params": {
            "last_auto_ccd": 1450.0, "last_auto_liquid": 25.0, "last_yinjing_target": 1440.0,
            "last_yinjing_liquid": 35.0, "current_auto_ccd": 1460.0, "target_temp": 1450.0,
            "manual_adjusted": False
        },
        "expected_range": (-5.0, 5.0)  # 应被限制在这个范围内
    }
]

# 测试用例汇总
all_test_cases = {
    "normal_optimization": test_data_1,
    "manual_adjusted": test_data_2,
    "small_temperature_change": test_data_3,
    "traditional_only": test_data_4,
    "incomplete_params": test_data_5,
    "extreme_values": test_data_6,
    "api_call": api_test_data,
    "boundary_tests": boundary_test_data
}

if __name__ == "__main__":
    print("液口距校准v3版本测试数据")
    print("=" * 50)
    
    for name, data in all_test_cases.items():
        if name != "boundary_tests":
            print(f"\n{name}:")
            if "x" in data:
                print(f"  传统参数数量: {len(data['x'])}")
            if "optimization_params" in data and data["optimization_params"]:
                print(f"  优化参数数量: {len(data['optimization_params'])}")
            if "expected" in data:
                print(f"  预期结果: {data['expected']}")
    
    print(f"\n边界测试用例数量: {len(boundary_test_data)}")
    print("\n使用方法:")
    print("from test_data import all_test_cases")
    print("test_case = all_test_cases['normal_optimization']")
    print("result = model.predict(test_case['x'], test_case['optimization_params'])")
