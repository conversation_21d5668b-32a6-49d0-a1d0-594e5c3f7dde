# 液口距校准v3版本测试指南

## 🎯 测试数据说明

我为您准备了完整的测试数据集，包含6个主要测试场景和边界测试用例。

## 📊 测试数据集

### 1. **正常优化模式** (`test_data_1`)
```python
# 上一炉数据
last_auto_ccd: 1450.0°C        # 上次自动定埚位完成后CCD温度
last_auto_liquid: 25.0mm       # 上次自动定埚位完成后绝对液口距
last_yinjing_target: 1448.0°C  # 上次引晶前目标温度
last_yinjing_liquid: 26.0mm    # 上次引晶前绝对液口距

# 本次数据
current_auto_ccd: 1452.0°C     # 本次自动定埚位完成后CCD温度
target_temp: 1449.0°C          # 本次引晶前目标温度

# 预期计算过程
上次温度偏差: -2.0°C (1448.0 - 1450.0)
上次液口距变化: 1.0mm (26.0 - 25.0)
比例系数: -0.5 mm/°C (1.0 / -2.0)
本次温度偏差: -3.0°C (1449.0 - 1452.0)
预期修正量: 1.5mm (-0.5 × -3.0)
```

### 2. **人工调整模式** (`test_data_2`)
```python
manual_adjusted: True          # 人工调整过埚位
# 预期结果: 跳过优化算法，返回 0.0mm
```

### 3. **温度变化很小** (`test_data_3`)
```python
last_yinjing_target: 1450.05°C # 温度变化很小 (0.05°C)
# 预期结果: 使用默认比例系数 0.1，修正量 -0.3mm
```

### 4. **纯传统模式** (`test_data_4`)
```python
optimization_params: None      # 不传入优化参数
# 预期结果: 使用传统模型，返回 0.0mm (模型不可用)
```

### 5. **优化参数不完整** (`test_data_5`)
```python
# 缺少部分优化参数
# 预期结果: 回退到传统模型，返回 0.0mm
```

### 6. **极端值测试** (`test_data_6`)
```python
last_yinjing_target: 1400.0°C  # 极端温度差 (-50°C)
last_yinjing_liquid: 30.0mm    # 极端液口距变化 (5.0mm)
# 预期结果: 比例系数被限制在 [-1.0, 1.0] 范围内
```

## 🧪 快速测试方法

### 方法1：运行完整测试
```bash
cd yekouju_adjust/beta_version/v3
python run_test.py
```

### 方法2：单独测试某个场景
```python
from test_data import all_test_cases
from model import YekoujuAdjustModel

# 初始化模型
model = YekoujuAdjustModel()

# 选择测试用例
test_case = all_test_cases['normal_optimization']

# 运行测试
result = model.predict(test_case['x'], test_case['optimization_params'])
print(f"结果: {result}mm")

# 验证结果
expected = test_case['expected']['correction']
print(f"预期: {expected}mm")
print(f"匹配: {'✅' if abs(result - expected) < 0.01 else '❌'}")
```

### 方法3：测试API调用格式
```python
from test_data import all_test_cases

# 获取API测试数据
api_data = all_test_cases['api_call']['request_data']

# 模拟API调用
# 这里可以直接用于测试您的API接口
```

## 📋 测试检查清单

### ✅ 功能测试
- [ ] 正常优化模式计算正确
- [ ] 人工调整时跳过优化算法
- [ ] 温度变化小时使用默认比例系数
- [ ] 纯传统模式正常回退
- [ ] 参数不完整时正常回退
- [ ] 极端值被正确限制

### ✅ 边界测试
- [ ] 修正量在合理范围内 (-5.0 ~ 5.0mm)
- [ ] 比例系数在合理范围内 (-1.0 ~ 1.0 mm/°C)
- [ ] 最终结果在限制范围内 (-3.0 ~ 3.0mm)

### ✅ 异常测试
- [ ] 缺少参数时不报错
- [ ] 无效数据时不报错
- [ ] 计算异常时返回安全值

## 🎯 预期测试结果

```
=== 测试: normal_optimization ===
✅ 优化算法计算: 上次ΔT=-2.00°C, ΔL=1.00mm, 比例系数r=-0.5000, 本次ΔT=-3.00°C, 修正量=1.50mm
预测结果: 1.5mm
✅ 结果正确: 预期 1.5mm, 实际 1.5mm

=== 测试: manual_adjusted ===
检测到人工调整过埚位，跳过优化算法
预测结果: 0.0mm
✅ 结果正确: 预期 0.0mm, 实际 0.0mm

=== 测试: small_temperature_change ===
上次温度变化太小(0.05°C)，使用默认比例系数: 0.1
✅ 优化算法计算: 上次ΔT=0.05°C, ΔL=0.50mm, 比例系数r=0.1000, 本次ΔT=-3.00°C, 修正量=-0.30mm
预测结果: -0.3mm
✅ 结果正确: 预期 -0.3mm, 实际 -0.3mm

=== 测试: traditional_only ===
传统模型不可用，返回默认值0
预测结果: 0.0mm
✅ 结果正确: 预期 0.0mm, 实际 0.0mm

测试总结:
成功: 6/6
✅ 所有测试通过！
```

## 💡 使用建议

1. **先运行完整测试**：确保所有功能正常
2. **重点测试优化模式**：这是v3版本的核心功能
3. **验证边界情况**：确保极端值被正确处理
4. **测试API格式**：确保与现有系统兼容

## 📞 如需帮助

如果测试过程中遇到问题，可以：
1. 检查模型文件是否存在
2. 确认Python路径设置正确
3. 查看控制台输出的详细信息
4. 对比预期结果和实际结果

**测试数据已准备完毕，您可以开始测试了！** 🚀
