"""
液口距校准调用接口 v3版本 - 优化版本
实现基于温度偏差的液口距校准修正量计算
"""

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel
import numpy as np
import datetime
import logging

# 初始化模型
yekouju_model = YekoujuAdjustModel()

def filter_absolute_liquid(recent_data):
    """
    对绝对液口距历史数据进行滤波处理
    使用中位数滤波 + 移动平均的组合滤波算法
    返回None表示无法滤波，应使用原始值
    """
    if not recent_data or len(recent_data) == 0:
        return None

    # 转换为numpy数组并去除无效值
    data_array = np.array(recent_data)
    valid_data = data_array[~np.isnan(data_array)]

    if len(valid_data) == 0:
        return None

    # 如果数据点少于3个，直接返回平均值
    if len(valid_data) < 3:
        return float(np.mean(valid_data))

    # 使用四分位数方法检测和去除异常值（更稳健）
    q1 = np.percentile(valid_data, 25)
    q3 = np.percentile(valid_data, 75)
    iqr = q3 - q1

    # 定义异常值边界（1.5倍IQR规则）
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr

    # 过滤异常值
    filtered_data = valid_data[(valid_data >= lower_bound) & (valid_data <= upper_bound)]

    # 如果过滤后没有数据，使用中位数
    if len(filtered_data) == 0:
        return float(np.median(valid_data))

    # 移动平均：对滤波后的数据取平均值
    return float(np.mean(filtered_data))

def save_auto_dingguo_data(request):
    """
    保存自动定埚位完成后的数据
    
    请求参数:
        device_id: 设备ID
        furnace_id: 炉次ID
        ccd_temp_list: CCD温度列表（10-12分钟的数据）
        absolute_liquid_list: 绝对液口距列表
        guowei_list: 埚位值列表
    """
    if request.method == 'POST' or request.method == 'GET':
        try:
            device_id = request.json.get('device_id')
            furnace_id = request.json.get('furnace_id')
            ccd_temp_list = request.json.get('ccd_temp_list', [])
            absolute_liquid_list = request.json.get('absolute_liquid_list', [])
            guowei_list = request.json.get('guowei_list', [])
            
            if not device_id or not furnace_id:
                return {"error": "缺少必要参数: device_id, furnace_id"}
            
            # 计算平均值
            ccd_temp_avg = np.mean([x for x in ccd_temp_list if x is not None and not np.isnan(x)])
            absolute_liquid_avg = np.mean([x for x in absolute_liquid_list if x is not None and not np.isnan(x)])
            guowei_avg = np.mean([x for x in guowei_list if x is not None and not np.isnan(x)])
            
            # 保存数据
            success = yekouju_model.save_auto_dingguo_data(
                device_id, furnace_id, ccd_temp_avg, absolute_liquid_avg, guowei_avg,
                len(ccd_temp_list)
            )
            
            # 更新状态
            yekouju_model.history_manager.update_status(
                device_id, furnace_id=furnace_id, auto_dingguo_completed=True
            )
            
            return {
                "success": success,
                "message": "自动定埚位数据保存成功" if success else "数据保存失败",
                "data": {
                    "ccd_temp_avg": round(ccd_temp_avg, 2),
                    "absolute_liquid_avg": round(absolute_liquid_avg, 2),
                    "guowei_avg": round(guowei_avg, 2),
                    "data_count": len(ccd_temp_list)
                }
            }
            
        except Exception as e:
            logging.error(f"保存自动定埚位数据失败: {e}")
            return {"error": f"保存数据失败: {str(e)}"}

def save_before_yinjing_data(request):
    """
    保存引晶前的数据
    
    请求参数:
        device_id: 设备ID
        furnace_id: 炉次ID
        ccd_temp: 引晶前CCD温度
        absolute_liquid: 引晶前绝对液口距
        guowei_value: 引晶前埚位值
        target_temp: 引晶前目标温度
    """
    if request.method == 'POST' or request.method == 'GET':
        try:
            device_id = request.json.get('device_id')
            furnace_id = request.json.get('furnace_id')
            ccd_temp = request.json.get('ccd_temp')
            absolute_liquid = request.json.get('absolute_liquid')
            guowei_value = request.json.get('guowei_value')
            target_temp = request.json.get('target_temp')
            
            if not all([device_id, furnace_id, ccd_temp is not None, 
                       absolute_liquid is not None, guowei_value is not None, 
                       target_temp is not None]):
                return {"error": "缺少必要参数"}
            
            # 保存数据
            success = yekouju_model.save_before_yinjing_data(
                device_id, furnace_id, ccd_temp, absolute_liquid, guowei_value, target_temp
            )
            
            return {
                "success": success,
                "message": "引晶前数据保存成功" if success else "数据保存失败",
                "data": {
                    "ccd_temp": ccd_temp,
                    "absolute_liquid": absolute_liquid,
                    "guowei_value": guowei_value,
                    "target_temp": target_temp
                }
            }
            
        except Exception as e:
            logging.error(f"保存引晶前数据失败: {e}")
            return {"error": f"保存数据失败: {str(e)}"}

def mark_manual_adjustment(request):
    """
    标记人工调整过埚位
    
    请求参数:
        device_id: 设备ID
    """
    if request.method == 'POST' or request.method == 'GET':
        try:
            device_id = request.json.get('device_id')
            
            if not device_id:
                return {"error": "缺少必要参数: device_id"}
            
            success = yekouju_model.mark_manual_adjustment(device_id)
            
            return {
                "success": success,
                "message": "人工调整标记成功" if success else "标记失败"
            }
            
        except Exception as e:
            logging.error(f"标记人工调整失败: {e}")
            return {"error": f"标记失败: {str(e)}"}

def yekoujuadjust_optimized(request):
    """
    优化版液口距校准接口

    请求参数:
        # 优化算法参数（完整数据模式，推荐）
        last_auto_ccd: 上次自动定埚位完成后CCD温度
        last_auto_liquid: 上次自动定埚位完成后绝对液口距
        last_yinjing_target: 上次引晶前目标温度
        last_yinjing_liquid: 上次引晶前绝对液口距
        current_auto_ccd: 本次自动定埚位完成后CCD温度
        target_temp: 本次引晶前目标温度
        manual_adjusted: 是否人工调整过埚位（可选）

        # 或者使用历史数据库模式
        device_id: 设备ID
        current_auto_ccd: 本次自动定埚位完成后的CCD温度
        target_temp: 本次引晶前目标温度

        # 传统参数（用于回退）
        daoliutong_up, daoliutong_down, daoliutong_left, daoliutong_right,
        daoliutong_upleft, daoliutong_upright, dingguo_finish_yewen,
        dingguo_finish_guowei, dingguo_finish_yekouju
    """
    if request.method == 'POST' or request.method == 'GET':
        try:
            # 获取优化算法参数（完整数据模式）
            last_auto_ccd = request.json.get('last_auto_ccd')
            last_auto_liquid = request.json.get('last_auto_liquid')
            last_yinjing_target = request.json.get('last_yinjing_target')
            last_yinjing_liquid = request.json.get('last_yinjing_liquid')
            current_auto_ccd = request.json.get('current_auto_ccd')
            target_temp = request.json.get('target_temp')
            manual_adjusted = request.json.get('manual_adjusted', False)

            # 获取历史数据库模式参数
            device_id = request.json.get('device_id')

            # 获取传统参数（用于回退）
            daoliutong_up = request.json.get('daoliutong_up')
            daoliutong_down = request.json.get('daoliutong_down')
            daoliutong_left = request.json.get('daoliutong_left')
            daoliutong_right = request.json.get('daoliutong_right')
            daoliutong_upleft = request.json.get('daoliutong_upleft')
            daoliutong_upright = request.json.get('daoliutong_upright')
            dingguo_finish_yewen = request.json.get('dingguo_finish_yewen')
            dingguo_finish_guowei = request.json.get('dingguo_finish_guowei')
            dingguo_finish_yekouju = request.json.get('dingguo_finish_yekouju')

            # 获取历史数据进行滤波
            recent_absolute_liquid = request.json.get('recent_absolute_liquid', [])
            absolute_liquid_filter = filter_absolute_liquid(recent_absolute_liquid)

            if absolute_liquid_filter is not None:
                dingguo_finish_yekouju = absolute_liquid_filter

            # 构建数据字典
            data = {
                # 优化算法参数（完整数据模式）
                "last_auto_ccd": last_auto_ccd,
                "last_auto_liquid": last_auto_liquid,
                "last_yinjing_target": last_yinjing_target,
                "last_yinjing_liquid": last_yinjing_liquid,
                "current_auto_ccd": current_auto_ccd,
                "target_temp": target_temp,
                "manual_adjusted": manual_adjusted,

                # 历史数据库模式参数
                "device_id": device_id,

                # 传统模型参数
                "daoliutong_up": daoliutong_up,
                "daoliutong_down": daoliutong_down,
                "daoliutong_left": daoliutong_left,
                "daoliutong_right": daoliutong_right,
                "daoliutong_upleft": daoliutong_upleft,
                "daoliutong_upright": daoliutong_upright,
                "dingguo_finish_yewen": dingguo_finish_yewen,
                "dingguo_finish_guowei": dingguo_finish_guowei,
                "dingguo_finish_yekouju": dingguo_finish_yekouju
            }

            # 进行预测
            results = yekouju_model.predict(data)
            results = round(results, 2)
            
            # 限制结果范围
            if results < -3:
                results = -3
            elif results > 3:
                results = 3

            return {
                "guowei_adjust": results,
                "version": "v3_optimized",
                "absolute_liquid_filter": absolute_liquid_filter if absolute_liquid_filter is not None else dingguo_finish_yekouju,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
        except Exception as e:
            logging.error(f"优化预测失败: {e}")
            # 预测失败时设置为0，相当于不做调整
            return {
                "guowei_adjust": 0,
                "version": "v3_optimized",
                "error": str(e),
                "timestamp": datetime.datetime.now().isoformat()
            }

# 兼容传统接口
def yekoujuadjust(request):
    """
    传统液口距校准接口（向后兼容）
    """
    return yekoujuadjust_optimized(request)
