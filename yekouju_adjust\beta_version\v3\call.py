"""
液口距校准调用接口 v3版本 - 优化版本
基于温度偏差的液口距校准修正量计算
简化版本：参考v1结构，在call.py中明确定义所有数据获取
"""

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel
import numpy as np
import datetime

# 初始化模型
yekouju_model = YekoujuAdjustModel()

def filter_absolute_liquid(recent_data):
    """
    对绝对液口距历史数据进行滤波处理
    使用中位数滤波 + 移动平均的组合滤波算法
    返回None表示无法滤波，应使用原始值
    """
    if not recent_data or len(recent_data) == 0:
        return None

    # 转换为numpy数组并去除无效值
    data_array = np.array(recent_data)
    valid_data = data_array[~np.isnan(data_array)]

    if len(valid_data) == 0:
        return None

    # 如果数据点少于3个，直接返回平均值
    if len(valid_data) < 3:
        return float(np.mean(valid_data))

    # 使用四分位数方法检测和去除异常值（更稳健）
    q1 = np.percentile(valid_data, 25)
    q3 = np.percentile(valid_data, 75)
    iqr = q3 - q1

    # 定义异常值边界（1.5倍IQR规则）
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr

    # 过滤异常值
    filtered_data = valid_data[(valid_data >= lower_bound) & (valid_data <= upper_bound)]

    # 如果过滤后没有数据，使用中位数
    if len(filtered_data) == 0:
        return float(np.median(valid_data))

    # 移动平均：对滤波后的数据取平均值
    return float(np.mean(filtered_data))



# 液口距校准推送（参考v1版本结构）
def yekoujuadjust(request):
    """
    液口距校准接口 v3版本 - 优化版本

    请求参数:
        # === 优化算法参数（推荐使用） ===
        # 上一炉数据（自动定埚位完成后10-12分钟平均值）
        last_auto_ccd: 上次自动定埚位完成后CCD温度平均值
        last_auto_liquid: 上次自动定埚位完成后绝对液口距平均值

        # 上一炉数据（引晶前）
        last_yinjing_target: 上次引晶前目标温度
        last_yinjing_liquid: 上次引晶前绝对液口距

        # 本次数据（自动定埚位完成后10-12分钟平均值）
        current_auto_ccd: 本次自动定埚位完成后CCD温度平均值

        # 本次数据（引晶前）
        target_temp: 本次引晶前目标温度

        # 控制参数
        manual_adjusted: 是否人工调整过埚位（可选，默认False）

        # === 传统参数（用于回退） ===
        daoliutong_up, daoliutong_down, daoliutong_left, daoliutong_right,
        daoliutong_upleft, daoliutong_upright, dingguo_finish_yewen,
        dingguo_finish_guowei, dingguo_finish_yekouju, recent_absolute_liquid
    """
    if request.method == 'POST' or request.method == 'GET':
        try:
            # === 获取优化算法参数 ===
            # 上一炉数据
            last_auto_ccd = request.json.get('last_auto_ccd')
            last_auto_liquid = request.json.get('last_auto_liquid')
            last_yinjing_target = request.json.get('last_yinjing_target')
            last_yinjing_liquid = request.json.get('last_yinjing_liquid')

            # 本次数据
            current_auto_ccd = request.json.get('current_auto_ccd')
            target_temp = request.json.get('target_temp')

            # 控制参数
            manual_adjusted = request.json.get('manual_adjusted', False)

            # === 获取传统参数（用于回退） ===
            daoliutong_up = request.json.get('daoliutong_up')
            daoliutong_down = request.json.get('daoliutong_down')
            daoliutong_left = request.json.get('daoliutong_left')
            daoliutong_right = request.json.get('daoliutong_right')
            daoliutong_upleft = request.json.get('daoliutong_upleft')
            daoliutong_upright = request.json.get('daoliutong_upright')
            dingguo_finish_yewen = request.json.get('dingguo_finish_yewen')
            dingguo_finish_guowei = request.json.get('dingguo_finish_guowei')
            dingguo_finish_yekouju = request.json.get('dingguo_finish_yekouju')

            # 获取历史数据进行滤波（保持v2版本的滤波功能）
            recent_absolute_liquid = request.json.get('recent_absolute_liquid', [])
            absolute_liquid_filter = filter_absolute_liquid(recent_absolute_liquid)

            if absolute_liquid_filter is not None:
                dingguo_finish_yekouju = absolute_liquid_filter

            # === 构建数据字典（参考v1版本结构） ===
            data = {
                # 优化算法参数
                "last_auto_ccd": last_auto_ccd,
                "last_auto_liquid": last_auto_liquid,
                "last_yinjing_target": last_yinjing_target,
                "last_yinjing_liquid": last_yinjing_liquid,
                "current_auto_ccd": current_auto_ccd,
                "target_temp": target_temp,
                "manual_adjusted": manual_adjusted,

                # 传统模型参数
                "daoliutong_up": daoliutong_up,
                "daoliutong_down": daoliutong_down,
                "daoliutong_left": daoliutong_left,
                "daoliutong_right": daoliutong_right,
                "daoliutong_upleft": daoliutong_upleft,
                "daoliutong_upright": daoliutong_upright,
                "dingguo_finish_yewen": dingguo_finish_yewen,
                "dingguo_finish_guowei": dingguo_finish_guowei,
                "dingguo_finish_yekouju": dingguo_finish_yekouju
            }

            # === 进行预测（参考v1版本结构） ===
            results = yekouju_model.predict(data)
            results = round(results, 2)

            # 限制结果范围（保持与v1版本一致）
            if results < -3:
                results = -3
            elif results > 3:
                results = 3
            else:
                results = results

            # 返回结果（参考v1版本格式，增加v3版本信息）
            return {
                "guowei_adjust": results,
                "version": "v3_optimized",
                "absolute_liquid_filter": absolute_liquid_filter if absolute_liquid_filter is not None else dingguo_finish_yekouju,
                "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            print(f"液口距校准预测失败: {e}")
            # 预测失败时设置为0，相当于不做调整
            return {
                "guowei_adjust": 0,
                "version": "v3_optimized",
                "error": str(e),
                "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
