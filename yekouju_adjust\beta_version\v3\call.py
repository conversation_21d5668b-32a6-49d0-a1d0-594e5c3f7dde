"""
液口距校准调用接口 v3版本 - 优化版本
基于温度偏差的液口距校准修正量计算
简化版本：参考v1结构，在call.py中明确定义所有数据获取
"""

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel

# 初始化模型
yekouju_model = YekoujuAdjustModel()



# 液口距校准推送（参考v1版本结构）
def yekoujuadjust(request):
    """
    液口距校准接口 v3版本 - 优化版本

    请求参数:
        # === 优化算法参数（推荐使用） ===
        # 上一炉数据（自动定埚位完成后10-12分钟数据列表）
        last_auto_ccd: 上次自动定埚位完成后CCD温度列表（需计算平均值）
        last_auto_yekouju: 上次自动定埚位完成后绝对液口距列表（需计算平均值）
        last_auto_guowei: 上次自动定埚位完成后埚位值（单个数值）

        # 上一炉数据（引晶前）
        last_yinjing_ccd: 上次引晶前CCD温度列表（需计算平均值）
        last_yinjing_yekouju: 上次引晶前绝对液口距列表（需计算平均值）
        last_yinjing_guowei: 上次引晶前埚位值（单个数值）

        # 本次数据（自动定埚位完成后10-12分钟数据列表）
        current_auto_ccd: 本次自动定埚位完成后CCD温度列表（需计算平均值）

        # 本次数据（引晶前）
        current_yinjing_target: 本次引晶前目标温度（单个数值）

        # === 传统参数（用于回退） ===
        daoliutong_up, daoliutong_down, daoliutong_left, daoliutong_right,
        daoliutong_upleft, daoliutong_upright, dingguo_finish_yewen,
        dingguo_finish_guowei, dingguo_finish_yekouju
    """
    if request.method == 'POST' or request.method == 'GET':
        try:
            # === 获取优化算法参数 ===
            # 上一炉数据（列表格式，在model.py中计算平均值）
            last_auto_ccd = request.json.get('last_auto_ccd')  # 列表
            last_auto_yekouju = request.json.get('last_auto_yekouju')  # 列表
            last_auto_guowei = request.json.get('last_auto_guowei')  # 单个数值
            last_yinjing_ccd = request.json.get('last_yinjing_ccd')  # 列表
            last_yinjing_yekouju = request.json.get('last_yinjing_yekouju')  # 列表
            last_yinjing_guowei = request.json.get('last_yinjing_guowei')  # 单个数值

            # 本次数据
            current_auto_ccd = request.json.get('current_auto_ccd')  # 列表
            current_yinjing_target = request.json.get('current_yinjing_target')  # 单个数值

            # === 获取传统参数（用于回退） ===
            daoliutong_up = request.json.get('daoliutong_up')
            daoliutong_down = request.json.get('daoliutong_down')
            daoliutong_left = request.json.get('daoliutong_left')
            daoliutong_right = request.json.get('daoliutong_right')
            daoliutong_upleft = request.json.get('daoliutong_upleft')
            daoliutong_upright = request.json.get('daoliutong_upright')
            dingguo_finish_yewen = request.json.get('dingguo_finish_yewen')
            dingguo_finish_guowei = request.json.get('dingguo_finish_guowei')
            dingguo_finish_yekouju = request.json.get('dingguo_finish_yekouju')



            # === 构建传统模型参数字典（保持参数名x不变） ===
            x = {
                "daoliutong_up": daoliutong_up,
                "daoliutong_down": daoliutong_down,
                "daoliutong_left": daoliutong_left,
                "daoliutong_right": daoliutong_right,
                "daoliutong_upleft": daoliutong_upleft,
                "daoliutong_upright": daoliutong_upright,
                "dingguo_finish_yewen": dingguo_finish_yewen,
                "dingguo_finish_guowei": dingguo_finish_guowei,
                "dingguo_finish_yekouju": dingguo_finish_yekouju
            }

            # === 构建优化算法参数字典 ===
            optimization_params = None
            if all(param is not None for param in [last_auto_ccd, last_auto_yekouju, last_auto_guowei,
                                                  last_yinjing_ccd, last_yinjing_yekouju, last_yinjing_guowei,
                                                  current_auto_ccd, current_yinjing_target]):
                optimization_params = {
                    "last_auto_ccd": last_auto_ccd,  # 列表
                    "last_auto_yekouju": last_auto_yekouju,  # 列表
                    "last_auto_guowei": last_auto_guowei,  # 单个数值
                    "last_yinjing_ccd": last_yinjing_ccd,  # 列表
                    "last_yinjing_yekouju": last_yinjing_yekouju,  # 列表
                    "last_yinjing_guowei": last_yinjing_guowei,  # 单个数值
                    "current_auto_ccd": current_auto_ccd,  # 列表
                    "current_yinjing_target": current_yinjing_target  # 单个数值
                }

            # === 进行预测（传统参数名x + 优化参数字典） ===
            results = yekouju_model.predict(x, optimization_params)
            results = round(results, 2)

            # 限制结果范围（保持与v1版本一致）
            if results < -3:
                results = -3
            elif results > 3:
                results = 3
            else:
                results = results

            # 返回结果（参考v1版本格式，增加v3版本信息）
            return {
                "guowei_adjust": results,
                "version": "v3_optimized"
            }

        except Exception as e:
            print(f"液口距校准预测失败: {e}")
            # 预测失败时设置为0，相当于不做调整
            return {
                "guowei_adjust": 0,
                "version": "v3_optimized",
                "error": str(e)
            }
