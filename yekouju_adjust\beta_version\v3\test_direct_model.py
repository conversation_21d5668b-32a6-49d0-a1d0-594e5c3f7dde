#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
液口距校准v3版本 - 直接模型测试

测试目标：
1. 直接测试模型的predict方法
2. 验证所有错误处理和边界情况
3. 确保模型在各种输入下都能稳定运行

创建时间: 2025-07-31
"""

import sys
import os
import time
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import YekoujuAdjustModel

def test_direct_model_normal():
    """测试模型正常调用"""
    print("=" * 80)
    print("直接模型测试1: 正常调用")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    # 正常的优化参数
    optimization_params = {
        "last_auto_ccd": [1449.5, 1450.0, 1450.5],
        "last_auto_yekouju": [24.8, 25.0, 25.2],
        "last_auto_guowei": 100.0,
        "last_yinjing_ccd": [1447.5, 1448.0, 1448.5],
        "last_yinjing_yekouju": [25.8, 26.0, 26.2],
        "last_yinjing_guowei": 101.0,
        "current_auto_ccd": [1451.5, 1452.0, 1452.5]
    }
    
    # 传统参数
    traditional_params = {
        "daoliutong_up": 1.0, "daoliutong_down": 1.0,
        "daoliutong_left": 1.0, "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0, "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0, "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    try:
        result = model.predict(traditional_params, optimization_params)
        print(f"✅ 模型调用成功: {result}mm")
        
        # 验证结果
        assert isinstance(result, (int, float)), f"返回值类型错误: {type(result)}"
        assert -20 <= result <= 20, f"返回值超出合理范围: {result}"
        
        return True
    except Exception as e:
        print(f"❌ 模型调用失败: {e}")
        traceback.print_exc()
        return False

def test_direct_model_edge_cases():
    """测试模型边界情况"""
    print("=" * 80)
    print("直接模型测试2: 边界情况")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    test_cases = [
        ("None参数", None, None),
        ("空字典", {}, {}),
        ("无效优化参数", {}, {"invalid": "data"}),
        ("极端温度", {}, {
            "last_auto_ccd": [2000.0], "last_auto_yekouju": [25.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1000.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1500.0]
        }),
        ("极端液口距", {}, {
            "last_auto_ccd": [1450.0], "last_auto_yekouju": [0.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [100.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
        ("极端埚位", {}, {
            "last_auto_ccd": [1450.0], "last_auto_yekouju": [25.0], "last_auto_guowei": 50.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 150.0,
            "current_auto_ccd": [1452.0]
        }),
    ]
    
    success_count = 0
    for name, traditional_params, optimization_params in test_cases:
        try:
            result = model.predict(traditional_params, optimization_params)
            print(f"✅ {name}测试通过: {result}mm")
            assert isinstance(result, (int, float)), f"{name}: 返回值类型错误"
            success_count += 1
        except Exception as e:
            print(f"❌ {name}测试失败: {e}")
            traceback.print_exc()
    
    return success_count == len(test_cases)

def test_direct_model_invalid_data():
    """测试模型无效数据处理"""
    print("=" * 80)
    print("直接模型测试3: 无效数据处理")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    test_cases = [
        ("空列表", {
            "last_auto_ccd": [], "last_auto_yekouju": [25.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
        ("NaN值", {
            "last_auto_ccd": [float('nan')], "last_auto_yekouju": [25.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
        ("无穷大值", {
            "last_auto_ccd": [1450.0], "last_auto_yekouju": [float('inf')], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
        ("字符串数据", {
            "last_auto_ccd": ["abc"], "last_auto_yekouju": [25.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
        ("非列表类型", {
            "last_auto_ccd": "not_a_list", "last_auto_yekouju": [25.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
        ("无效埚位值", {
            "last_auto_ccd": [1450.0], "last_auto_yekouju": [25.0], "last_auto_guowei": "invalid",
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": [1452.0]
        }),
    ]
    
    success_count = 0
    for name, optimization_params in test_cases:
        try:
            result = model.predict({}, optimization_params)
            print(f"✅ {name}测试通过: {result}mm")
            assert isinstance(result, (int, float)), f"{name}: 返回值类型错误"
            success_count += 1
        except Exception as e:
            print(f"❌ {name}测试失败: {e}")
            traceback.print_exc()
    
    return success_count == len(test_cases)

def test_fixed_target_temperature():
    """测试固定目标温度"""
    print("=" * 80)
    print("直接模型测试4: 固定目标温度")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    # 验证固定目标温度常量
    expected_temp = 1449.75
    actual_temp = model.YINJING_TARGET_TEMP
    
    print(f"固定目标温度: {actual_temp}°C")
    assert actual_temp == expected_temp, f"固定目标温度错误: 期望{expected_temp}, 实际{actual_temp}"
    
    # 测试不同的current_auto_ccd值
    test_cases = [
        ([1450.0], -0.25),  # 1449.75 - 1450.0 = -0.25
        ([1449.0], 0.75),   # 1449.75 - 1449.0 = 0.75
        ([1452.0], -2.25),  # 1449.75 - 1452.0 = -2.25
    ]
    
    success_count = 0
    for current_ccd, expected_delta in test_cases:
        optimization_params = {
            "last_auto_ccd": [1450.0], "last_auto_yekouju": [25.0], "last_auto_guowei": 100.0,
            "last_yinjing_ccd": [1448.0], "last_yinjing_yekouju": [26.0], "last_yinjing_guowei": 101.0,
            "current_auto_ccd": current_ccd
        }
        
        try:
            result = model.predict({}, optimization_params)
            print(f"✅ CCD={current_ccd[0]}, 预期温度偏差={expected_delta}, 结果={result}mm")
            success_count += 1
        except Exception as e:
            print(f"❌ CCD={current_ccd[0]}测试失败: {e}")
    
    return success_count == len(test_cases)

def single_model_call(call_id):
    """单次模型调用（用于并发测试）"""
    model = YekoujuAdjustModel()
    
    optimization_params = {
        "last_auto_ccd": [1449.5 + call_id * 0.1, 1450.0, 1450.5],
        "last_auto_yekouju": [24.8, 25.0, 25.2],
        "last_auto_guowei": 100.0 + call_id * 0.1,
        "last_yinjing_ccd": [1447.5, 1448.0, 1448.5],
        "last_yinjing_yekouju": [25.8, 26.0, 26.2],
        "last_yinjing_guowei": 101.0 + call_id * 0.1,
        "current_auto_ccd": [1451.5, 1452.0, 1452.5]
    }
    
    try:
        result = model.predict({}, optimization_params)
        return f"模型调用{call_id}: {result}mm"
    except Exception as e:
        return f"模型调用{call_id}失败: {e}"

def test_concurrent_model_calls():
    """测试模型并发调用"""
    print("=" * 80)
    print("直接模型测试5: 并发调用")
    print("=" * 80)
    
    num_threads = 10
    calls_per_thread = 5
    total_calls = num_threads * calls_per_thread
    
    print(f"启动{num_threads}个线程，每个线程调用{calls_per_thread}次")
    
    start_time = time.time()
    success_count = 0
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        for i in range(total_calls):
            future = executor.submit(single_model_call, i)
            futures.append(future)
        
        for future in as_completed(futures):
            try:
                result = future.result(timeout=5)
                if "失败" not in result:
                    success_count += 1
                print(f"  {result}")
            except Exception as e:
                print(f"  并发调用异常: {e}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"并发测试完成: {success_count}/{total_calls} 成功, 耗时: {duration:.2f}秒")
    return success_count >= total_calls * 0.9  # 90%成功率即可

def main():
    """主测试函数"""
    print("液口距校准v3版本 - 直接模型测试")
    print("=" * 80)
    
    test_functions = [
        test_direct_model_normal,
        test_direct_model_edge_cases,
        test_direct_model_invalid_data,
        test_fixed_target_temperature,
        test_concurrent_model_calls,
    ]
    
    passed_tests = 0
    total_tests = len(test_functions)
    
    for test_func in test_functions:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_func.__name__} 通过")
            else:
                print(f"❌ {test_func.__name__} 失败")
        except Exception as e:
            print(f"❌ {test_func.__name__} 异常: {e}")
            traceback.print_exc()
        
        print()  # 空行分隔
    
    print("=" * 80)
    print(f"直接模型测试总结: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有直接模型测试通过！模型稳定性验证成功！")
        return True
    else:
        print("⚠️ 部分直接模型测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
