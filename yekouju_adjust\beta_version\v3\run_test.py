"""
使用测试数据运行液口距校准v3版本测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel
from yekouju_adjust.beta_version.v3.test_data import all_test_cases

def run_single_test(name, test_case):
    """运行单个测试用例"""
    print(f"\n=== 测试: {name} ===")
    
    model = YekoujuAdjustModel()
    
    try:
        # 获取测试数据
        x = test_case.get('x')
        optimization_params = test_case.get('optimization_params')
        expected = test_case.get('expected', {})
        
        # 运行预测
        result = model.predict(x, optimization_params)
        
        print(f"预测结果: {result}mm")
        
        # 验证结果
        if 'guowei_adjust' in expected:
            expected_value = expected['guowei_adjust']
            if abs(result - expected_value) < 0.01:
                print(f"✅ 结果正确: 预期 {expected_value}mm, 实际 {result}mm")
            else:
                print(f"❌ 结果不符: 预期 {expected_value}mm, 实际 {result}mm")
        elif 'correction' in expected:  # 兼容旧的测试数据
            expected_value = expected['correction']
            if abs(result - expected_value) < 0.01:
                print(f"✅ 结果正确: 预期 {expected_value}mm, 实际 {result}mm")
            else:
                print(f"❌ 结果不符: 预期 {expected_value}mm, 实际 {result}mm")
        
        # 显示计算详情
        if optimization_params and not optimization_params.get('manual_adjusted', False):
            if all(key in optimization_params for key in ['last_auto_ccd', 'last_auto_liquid', 
                                                         'last_yinjing_target', 'last_yinjing_liquid',
                                                         'current_auto_ccd', 'target_temp']):
                delta_t_last = optimization_params['last_yinjing_target'] - optimization_params['last_auto_ccd']
                delta_l_last = optimization_params['last_yinjing_liquid'] - optimization_params['last_auto_liquid']
                delta_t_current = optimization_params['target_temp'] - optimization_params['current_auto_ccd']
                
                print(f"计算详情:")
                print(f"  上次温度偏差: {delta_t_last}°C")
                print(f"  上次液口距变化: {delta_l_last}mm")
                print(f"  本次温度偏差: {delta_t_current}°C")
                
                if abs(delta_t_last) >= 0.1:
                    r = delta_l_last / delta_t_last
                    r_clipped = max(-1.0, min(1.0, r))
                    print(f"  比例系数: {r:.4f} → {r_clipped:.4f} (限制后)")
                else:
                    print(f"  使用默认比例系数: 0.1")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def run_all_tests():
    """运行所有测试用例"""
    print("液口距校准v3版本 - 完整测试")
    print("=" * 60)
    
    results = {}
    
    # 运行主要测试用例
    main_tests = ['normal_optimization', 'manual_adjusted', 'small_temperature_change', 
                  'traditional_only', 'incomplete_params', 'extreme_values']
    
    for test_name in main_tests:
        if test_name in all_test_cases:
            result = run_single_test(test_name, all_test_cases[test_name])
            results[test_name] = result
    
    # 运行边界测试
    print(f"\n=== 边界测试 ===")
    boundary_tests = all_test_cases.get('boundary_tests', [])
    model = YekoujuAdjustModel()
    
    for i, test_case in enumerate(boundary_tests):
        print(f"\n边界测试 {i+1}: {test_case['name']}")
        result = model.predict(test_case['x'], test_case['optimization_params'])
        expected_range = test_case['expected_range']
        
        print(f"结果: {result}mm")
        if expected_range[0] <= result <= expected_range[1]:
            print(f"✅ 在预期范围内: [{expected_range[0]}, {expected_range[1]}]")
        else:
            print(f"❌ 超出预期范围: [{expected_range[0]}, {expected_range[1]}]")
    
    # 测试总结
    print(f"\n" + "=" * 60)
    print("测试总结:")
    success_count = sum(1 for r in results.values() if r is not None)
    total_count = len(results)
    print(f"成功: {success_count}/{total_count}")
    
    return results

def test_api_call():
    """测试API调用格式"""
    print(f"\n=== API调用测试 ===")
    
    api_data = all_test_cases['api_call']['request_data']
    expected_response = all_test_cases['api_call']['expected_response']
    
    # 模拟API调用
    model = YekoujuAdjustModel()
    
    # 构建参数
    x = {
        "daoliutong_up": api_data["daoliutong_up"],
        "daoliutong_down": api_data["daoliutong_down"],
        "daoliutong_left": api_data["daoliutong_left"],
        "daoliutong_right": api_data["daoliutong_right"],
        "daoliutong_upleft": api_data["daoliutong_upleft"],
        "daoliutong_upright": api_data["daoliutong_upright"],
        "dingguo_finish_yewen": api_data["dingguo_finish_yewen"],
        "dingguo_finish_guowei": api_data["dingguo_finish_guowei"],
        "dingguo_finish_yekouju": api_data["dingguo_finish_yekouju"]
    }
    
    optimization_params = {
        "last_auto_ccd": api_data["last_auto_ccd"],
        "last_auto_liquid": api_data["last_auto_liquid"],
        "last_yinjing_target": api_data["last_yinjing_target"],
        "last_yinjing_liquid": api_data["last_yinjing_liquid"],
        "current_auto_ccd": api_data["current_auto_ccd"],
        "current_auto_liquid": api_data["current_auto_liquid"],
        "current_guowei": api_data["current_guowei"],
        "target_temp": api_data["target_temp"],
        "manual_adjusted": api_data["manual_adjusted"]
    }
    
    # 进行预测
    result = model.predict(x, optimization_params)
    
    # 模拟API响应
    response = {
        "guowei_adjust": result,
        "version": "v3_optimized"
    }
    
    print(f"API请求数据: {len(api_data)} 个参数")
    print(f"API响应: {response}")
    print(f"预期响应: {expected_response}")
    
    if abs(response['guowei_adjust'] - expected_response['guowei_adjust']) < 0.01:
        print("✅ API调用测试通过")
    else:
        print("❌ API调用测试失败")

if __name__ == "__main__":
    # 运行所有测试
    results = run_all_tests()
    
    # 测试API调用
    test_api_call()
    
    print(f"\n" + "=" * 60)
    print("测试完成！")
    print("\n如需单独测试某个用例，可以使用:")
    print("from test_data import all_test_cases")
    print("from model import YekoujuAdjustModel")
    print("model = YekoujuAdjustModel()")
    print("test_case = all_test_cases['normal_optimization']")
    print("result = model.predict(test_case['x'], test_case['optimization_params'])")
    print("print(f'结果: {result}mm')")
