"""
液口距校准优化模型简化测试
专注测试核心优化算法，不依赖传统模型
"""

import sys
import os
import tempfile
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuHistoryDataManager, TemperatureLiquidModel
import json

def test_core_algorithm():
    """测试核心算法"""
    print("=== 测试核心温度-液口距算法 ===")
    
    # 1. 测试温度-液口距模型
    model = TemperatureLiquidModel(default_r=0.1)
    
    # 模拟上一炉数据
    print("1. 上一炉数据:")
    last_auto_ccd = 1450.0      # 上次自动定埚位完成后CCD温度
    last_yinjing_target = 1448.0 # 上次引晶前目标温度
    last_auto_liquid = 25.0     # 上次自动定埚位完成后绝对液口距
    last_yinjing_liquid = 26.0  # 上次引晶前绝对液口距
    
    print(f"   自动定埚位完成后: CCD={last_auto_ccd}°C, 液口距={last_auto_liquid}mm")
    print(f"   引晶前: 目标温度={last_yinjing_target}°C, 液口距={last_yinjing_liquid}mm")
    
    # 计算上次偏差
    delta_t_last = last_yinjing_target - last_auto_ccd  # -2.0°C
    delta_l_last = last_yinjing_liquid - last_auto_liquid  # 1.0mm
    
    print(f"   温度偏差: {delta_t_last}°C")
    print(f"   液口距变化: {delta_l_last}mm")
    
    # 计算比例系数
    r = model.calculate_ratio(delta_t_last, delta_l_last)
    print(f"   计算得到比例系数 r = {r} mm/°C")
    
    # 2. 本次数据
    print("\n2. 本次数据:")
    current_auto_ccd = 1452.0   # 本次自动定埚位完成后CCD温度
    current_target = 1449.0     # 本次引晶前目标温度
    
    print(f"   自动定埚位完成后CCD: {current_auto_ccd}°C")
    print(f"   引晶前目标温度: {current_target}°C")
    
    # 计算本次温度偏差
    delta_t_current = current_target - current_auto_ccd  # -3.0°C
    print(f"   温度偏差: {delta_t_current}°C")
    
    # 计算修正量
    correction = model.predict_correction(r, delta_t_current)
    print(f"   预测液口距校准修正量: {correction}mm")
    
    # 3. 解释结果
    print("\n3. 结果解释:")
    print(f"   根据上一炉的经验，温度每降低1°C，液口距增加{r}mm")
    print(f"   本次温度偏差为{delta_t_current}°C，预计液口距需要调整{correction}mm")
    if correction > 0:
        print("   正值表示需要增加液口距（埚位下降）")
    elif correction < 0:
        print("   负值表示需要减少液口距（埚位上升）")
    else:
        print("   无需调整")

def test_data_management():
    """测试数据管理"""
    print("\n=== 测试数据管理功能 ===")
    
    # 创建临时数据库
    test_db_path = os.path.join(tempfile.gettempdir(), "test_yekouju_simple.sqlite")
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    manager = YekoujuHistoryDataManager(db_path=test_db_path)
    
    device_id = "A001"
    furnace_id = "F20250728001"
    
    print(f"1. 保存设备 {device_id} 炉次 {furnace_id} 的数据")
    
    # 保存自动定埚位后数据
    success1 = manager.save_data(
        device_id, furnace_id, 'auto_dingguo_after',
        ccd_temp=1450.0, absolute_liquid=25.0, guowei_value=100.0
    )
    print(f"   自动定埚位数据保存: {'✅ 成功' if success1 else '❌ 失败'}")
    
    # 保存引晶前数据
    success2 = manager.save_data(
        device_id, furnace_id, 'before_yinjing',
        ccd_temp=1448.0, absolute_liquid=26.0, guowei_value=101.0, target_temp=1448.0
    )
    print(f"   引晶前数据保存: {'✅ 成功' if success2 else '❌ 失败'}")
    
    # 获取历史数据
    last_data = manager.get_last_furnace_data(device_id)
    if last_data:
        print("2. 成功获取历史数据:")
        print(f"   自动定埚位后: {last_data.get('auto_dingguo_after', {})}")
        print(f"   引晶前: {last_data.get('before_yinjing', {})}")
    else:
        print("2. ❌ 获取历史数据失败")
    
    # 测试状态管理
    manager.update_status(device_id, furnace_id=furnace_id, auto_dingguo_completed=True)
    status = manager.get_status(device_id)
    if status:
        print(f"3. 设备状态: 炉次={status['current_furnace_id']}, 自动定埚位完成={status['auto_dingguo_completed']}")
    
    # 清理
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("4. ✅ 测试数据库已清理")

def test_complete_workflow():
    """测试完整工作流程"""
    print("\n=== 测试完整工作流程 ===")
    
    # 创建临时数据库
    test_db_path = os.path.join(tempfile.gettempdir(), "test_workflow.sqlite")
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    manager = YekoujuHistoryDataManager(db_path=test_db_path)
    temp_model = TemperatureLiquidModel()
    
    device_id = "A001"
    
    print("场景：模拟两炉连续生产")
    
    # === 第一炉 ===
    print("\n第一炉 (F001):")
    furnace1 = "F001"
    
    # 1. 自动定埚位完成后（10-12分钟平均值）
    manager.save_data(device_id, furnace1, 'auto_dingguo_after',
                     ccd_temp=1450.0, absolute_liquid=25.0, guowei_value=100.0)
    print("  ✅ 保存自动定埚位完成后数据: CCD=1450°C, 液口距=25.0mm")
    
    # 2. 引晶前
    manager.save_data(device_id, furnace1, 'before_yinjing',
                     ccd_temp=1448.0, absolute_liquid=26.0, guowei_value=101.0, target_temp=1448.0)
    print("  ✅ 保存引晶前数据: CCD=1448°C, 液口距=26.0mm, 目标=1448°C")
    
    # === 第二炉 ===
    print("\n第二炉 (F002):")
    furnace2 = "F002"
    
    # 1. 自动定埚位完成后
    current_auto_ccd = 1452.0
    current_target = 1449.0
    print(f"  本次自动定埚位完成后: CCD={current_auto_ccd}°C")
    print(f"  本次引晶前目标温度: {current_target}°C")
    
    # 2. 获取上一炉数据并计算
    last_data = manager.get_last_furnace_data(device_id)
    if last_data and 'auto_dingguo_after' in last_data and 'before_yinjing' in last_data:
        auto_data = last_data['auto_dingguo_after']
        yinjing_data = last_data['before_yinjing']
        
        # 计算上次偏差
        delta_t_last = yinjing_data['target_temp'] - auto_data['ccd_temp']
        delta_l_last = yinjing_data['absolute_liquid'] - auto_data['absolute_liquid']
        
        # 计算比例系数
        r = temp_model.calculate_ratio(delta_t_last, delta_l_last)
        
        # 计算本次修正量
        delta_t_current = current_target - current_auto_ccd
        correction = temp_model.predict_correction(r, delta_t_current)
        
        print(f"  📊 分析结果:")
        print(f"     上次温度偏差: {delta_t_last}°C")
        print(f"     上次液口距变化: {delta_l_last}mm")
        print(f"     计算比例系数: {r} mm/°C")
        print(f"     本次温度偏差: {delta_t_current}°C")
        print(f"     🎯 建议液口距修正量: {correction}mm")
        
        if abs(correction) > 0.1:
            direction = "下降" if correction > 0 else "上升"
            print(f"     💡 建议: 埚位{direction} {abs(correction)}mm")
        else:
            print(f"     💡 建议: 无需调整")
    
    # 清理
    if os.path.exists(test_db_path):
        os.remove(test_db_path)

if __name__ == "__main__":
    print("液口距校准优化算法测试")
    print("=" * 60)
    
    try:
        test_core_algorithm()
        test_data_management()
        test_complete_workflow()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！优化算法工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
