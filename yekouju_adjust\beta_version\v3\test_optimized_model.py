"""
液口距校准优化模型测试脚本
测试基于温度偏差的液口距校准修正量计算功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel, YekoujuHistoryDataManager, TemperatureLiquidModel
import numpy as np
import json

def test_temperature_liquid_model():
    """测试温度-液口距模型"""
    print("=== 测试温度-液口距模型 ===")
    
    model = TemperatureLiquidModel(default_r=0.1)
    
    # 测试用例1：正常情况
    delta_t = 10.0  # 温度偏差10°C
    delta_l = 2.0   # 液口距变化2mm
    r = model.calculate_ratio(delta_t, delta_l)
    print(f"测试1 - 温度偏差: {delta_t}°C, 液口距变化: {delta_l}mm")
    print(f"计算得到比例系数 r: {r} mm/°C")
    
    # 预测修正量
    current_delta_t = 8.0
    correction = model.predict_correction(r, current_delta_t)
    print(f"当前温度偏差: {current_delta_t}°C, 预测修正量: {correction}mm")
    
    # 测试用例2：温度变化很小
    delta_t_small = 0.05
    r_small = model.calculate_ratio(delta_t_small, delta_l)
    print(f"\n测试2 - 温度偏差很小: {delta_t_small}°C")
    print(f"使用默认比例系数: {r_small} mm/°C")
    
    # 测试用例3：极端值
    delta_t_large = 50.0
    delta_l_large = 10.0
    r_large = model.calculate_ratio(delta_t_large, delta_l_large)
    correction_large = model.predict_correction(r_large, 20.0)
    print(f"\n测试3 - 极端值: 温度偏差{delta_t_large}°C, 液口距变化{delta_l_large}mm")
    print(f"比例系数: {r_large} mm/°C, 修正量: {correction_large}mm (应被限制在合理范围)")

def test_history_data_manager():
    """测试历史数据管理器"""
    print("\n=== 测试历史数据管理器 ===")
    
    # 使用临时数据库
    import tempfile
    test_db_path = os.path.join(tempfile.gettempdir(), "test_yekouju.sqlite")
    manager = YekoujuHistoryDataManager(db_path=test_db_path)
    
    device_id = "A001"
    furnace_id = "F20250728001"
    
    # 保存自动定埚位后数据
    success1 = manager.save_data(
        device_id, furnace_id, 'auto_dingguo_after',
        ccd_temp=1450.5, absolute_liquid=25.3, guowei_value=100.2
    )
    print(f"保存自动定埚位数据: {'成功' if success1 else '失败'}")
    
    # 保存引晶前数据
    success2 = manager.save_data(
        device_id, furnace_id, 'before_yinjing',
        ccd_temp=1448.2, absolute_liquid=26.1, guowei_value=100.8, target_temp=1448.0
    )
    print(f"保存引晶前数据: {'成功' if success2 else '失败'}")
    
    # 更新状态
    manager.update_status(device_id, furnace_id=furnace_id, auto_dingguo_completed=True)
    
    # 获取状态
    status = manager.get_status(device_id)
    print(f"设备状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    # 获取历史数据
    last_data = manager.get_last_furnace_data(device_id)
    print(f"历史数据: {json.dumps(last_data, indent=2, ensure_ascii=False)}")

def test_optimized_model():
    """测试优化模型"""
    print("\n=== 测试优化模型 ===")
    
    model = YekoujuAdjustModel()
    
    device_id = "A001"
    furnace_id = "F20250728002"
    
    # 模拟保存上一炉数据
    print("1. 保存上一炉自动定埚位数据...")
    model.save_auto_dingguo_data(
        device_id, "F20250728001", 
        ccd_temp_avg=1450.0, absolute_liquid_avg=25.0, guowei_avg=100.0, data_count=10
    )
    
    print("2. 保存上一炉引晶前数据...")
    model.save_before_yinjing_data(
        device_id, "F20250728001",
        ccd_temp=1448.0, absolute_liquid=26.0, guowei_value=101.0, target_temp=1448.0
    )
    
    # 测试优化计算
    print("3. 测试优化计算...")
    result = model.calculate_optimized_correction(
        device_id, current_auto_ccd=1452.0, target_temp=1449.0
    )
    print(f"优化计算结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 测试传统预测（回退）
    print("4. 测试传统预测...")
    traditional_data = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    traditional_result = model.predict(traditional_data)
    print(f"传统模型预测结果: {traditional_result}")
    
    # 测试集成预测
    print("5. 测试集成预测...")
    integrated_data = {
        "device_id": device_id,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        **traditional_data
    }
    integrated_result = model.predict(integrated_data)
    print(f"集成预测结果: {integrated_result}")
    
    # 测试人工调整标记
    print("6. 测试人工调整标记...")
    model.mark_manual_adjustment(device_id)
    
    # 再次测试优化计算（应该跳过）
    result_after_manual = model.calculate_optimized_correction(
        device_id, current_auto_ccd=1452.0, target_temp=1449.0
    )
    print(f"人工调整后的计算结果: {json.dumps(result_after_manual, indent=2, ensure_ascii=False)}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    model = YekoujuAdjustModel()
    
    # 测试无历史数据
    result1 = model.calculate_optimized_correction("NEW_DEVICE", 1450.0, 1448.0)
    print(f"无历史数据: {result1['message']}")
    
    # 测试数据不完整
    device_id = "INCOMPLETE_DATA"
    model.save_auto_dingguo_data(device_id, "F001", 1450.0, 25.0, 100.0, 10)
    # 不保存引晶前数据
    result2 = model.calculate_optimized_correction(device_id, 1450.0, 1448.0)
    print(f"数据不完整: {result2['message']}")

if __name__ == "__main__":
    print("液口距校准优化模型测试")
    print("=" * 50)
    
    try:
        test_temperature_liquid_model()
        test_history_data_manager()
        test_optimized_model()
        test_edge_cases()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
