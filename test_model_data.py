import sys
import os
sys.path.append('kongwen_power_control/beta_version/v6')

from model import KongwenGonglvCorrectionModel
import json

def test_model_with_data():
    print("=" * 60)
    print("测试 KongwenGonglvCorrectionModel 数据调用")
    print("=" * 60)
    
    # Setup 数据
    setup_data = {
        "device_id": "A3600",
        "buckets": [],
        "times": [],
        "jialiao": 587.0,
        "config": {
            "vice_ratios": [[1.0, 270.0, 17.0, 20.0], [271.0, 700.0, 17.0, 25.0]],
            "high_ratio": 90,
            "done_power_k": [1.3, 0.5, 1.1, 0.4],
            "begin_t": 10,
            "undo_main_ratio": 85,
            "done_ratio": 98,
            "down_time": 30,
            "vice_check_time": 20,
            "max_reopen_time": 15,
            "key_power_k": [1.5, 1.2, 1, 0.5, 0.5],
            "time_range": [18, 30],
            "vice_time_range": [10, 25],
            "melting_ccd3": [1452, 1460],
            "melting_power_k": [1.5, 0.5],
            "dynamic_vice_heating": 1,
            "kongwen_target": 1448,
            "turnover_threshold": 55,
            "film_threshold": 40,
            "adjust_space": 5,
            "turnover_delay": [5, 5],
            "one_vice_close_ratio": [17, 20],
            "ladder_vice_power": 0
        },
        "init_main_power": 100.0,
        "init_vice_power": 80.0,
        "yinjing_power": 54.5,
        "product_type": "11",
        "field_size": "36",
        "target_ccd": 1448.0,
        "history_data": [],
        "feeding_type": 1
    }
    
    # Predict 数据
    predict_data = {
        "device_id": "A3600",
        "t": 4694.0,
        "ratio": 99.9968,
        "ccd": -1.0,
        "ccd3": 1469.3,
        "fullmelting": 1,
        "sum_jialiao_time": 21333.0,
        "last_jialiao_time": 1182.0,
        "last_jialiao_weight": 29.5,
        "last_Interval_time": 2859.0,
        "barrelage": 7.0,
        "film_ratio": 0.0,
        "last_but_one_Interval_time": 4019.0,
        "last_but_one_jialiao_time": 2679.0,
        "last_but_one_jialiao_weight": 89.6,
        "turnover_ratio": 0.0,
        "cumulative_feed_weight": 616.5,
        "time_interval": 26027.0
    }
    
    try:
        print("\n1. 创建模型实例...")
        model = KongwenGonglvCorrectionModel()

        # 手动设置必要的默认配置，避免需要配置文件
        model.origin_vice_close_ratio = [25, 40]
        model.high_ratio = 90
        model.time_range = [18, 30]
        model.origin_done_power = [70, 50, 80, 40]
        model.done_power_k = [1.3, 0.5, 1.1, 0.4]
        model.origin_init_power = [100, 80]
        model.begin_t = 10
        model.undo_main_ratio = 85
        model.done_ratio = 98
        model.down_time = 30
        model.vice_check_time = 20
        model.max_reopen_time = 15
        model.key_ccd = [0, 10, 20, 30, 40, 50, 10000]
        model.origin_key_power = [100, 90, 80, 70, 60]
        model.key_power_k = [1.5, 1.2, 1, 0.5, 0.5]
        model.vice_time_range = [10, 25]
        model.melting_ccd3 = [1452, 1460]
        model.melting_power_k = [1.5, 0.5]
        model.dynamic_vice_heating = 1
        model.kongwen_target = 1448
        model.ladder_vice_power = 0
        model.turnover_threshold = 40
        model.film_threshold = 40
        model.adjust_space = 10
        model.turnover_delay = [5, 5]
        model.one_vice_close_ratio = [25, 40]
        model.main_delay = 5

        print("✅ 模型实例创建成功")
        
        print("\n2. 测试 setup 方法...")
        model.setup(
            device_id=setup_data["device_id"],
            jialiao=setup_data["jialiao"],
            times=setup_data["times"],
            power_yinjing=setup_data["yinjing_power"],
            init_power=[setup_data["init_main_power"], setup_data["init_vice_power"]],
            config=setup_data["config"],
            field_size=setup_data["field_size"],
            product_type=setup_data["product_type"],
            target_ccd=setup_data["target_ccd"],
            history_data=setup_data["history_data"],
            feeding_type=setup_data["feeding_type"]
        )
        print("✅ setup 方法调用成功")
        print(f"   - 设备ID: {setup_data['device_id']}")
        print(f"   - 加料量: {setup_data['jialiao']} kg")
        print(f"   - 引晶功率: {setup_data['yinjing_power']} kW")
        print(f"   - 初始主功率: {setup_data['init_main_power']} kW")
        print(f"   - 初始副功率: {setup_data['init_vice_power']} kW")
        print(f"   - 投料类型: {'复投' if setup_data['feeding_type'] == 1 else '首投'}")
        
        print("\n3. 测试 predict 方法...")
        result = model.predict(
            t=predict_data["t"],
            ratio=predict_data["ratio"],
            ccd=predict_data["ccd"],
            ccd3=predict_data["ccd3"],
            fullmelting=predict_data["fullmelting"],
            sum_jialiao_time=predict_data["sum_jialiao_time"],
            last_jialiao_time=predict_data["last_jialiao_time"],
            last_jialiao_weight=predict_data["last_jialiao_weight"],
            last_Interval_time=predict_data["last_Interval_time"],
            barrelage=predict_data["barrelage"],
            last_but_one_jialiao_weight=predict_data["last_but_one_jialiao_weight"],
            last_but_one_jialiao_time=predict_data["last_but_one_jialiao_time"],
            last_but_one_jialiao_interval_time=predict_data["last_but_one_Interval_time"],
            film_ratio=predict_data["film_ratio"],
            turnover_ratio=predict_data["turnover_ratio"],
            time_interval=predict_data["time_interval"],
            cumulative_feed_weight=predict_data["cumulative_feed_weight"]
        )
        
        print("✅ predict 方法调用成功")
        print(f"   - 输入时间: {predict_data['t']} 秒 ({predict_data['t']/60:.1f} 分钟)")
        print(f"   - 溶液比: {predict_data['ratio']:.2f}%")
        print(f"   - CCD温度: {predict_data['ccd']}°C")
        print(f"   - CCD3温度: {predict_data['ccd3']}°C")
        print(f"   - 累积加料重量: {predict_data['cumulative_feed_weight']} kg")
        print(f"   - 时间间隔: {predict_data['time_interval']} 秒 ({predict_data['time_interval']/3600:.2f} 小时)")
        
        # 解析返回结果
        if len(result) == 3:
            main_power, vice_power, vice_power_info = result
            print(f"\n4. 预测结果:")
            print(f"   - 主功率: {main_power:.2f} kW")
            print(f"   - 副功率: {vice_power:.2f} kW")
            
            if isinstance(vice_power_info, list) and len(vice_power_info) >= 3:
                real_time_vice_power, cumulative_power, predicted_total_power = vice_power_info
                print(f"   - 实时副功率: {real_time_vice_power:.2f} kW")
                print(f"   - 累积副功率: {cumulative_power:.2f} kWh")
                if predicted_total_power is not None:
                    print(f"   - 预测总副功率: {predicted_total_power:.2f} kWh")
                else:
                    print(f"   - 预测总副功率: None (降级机制)")
            else:
                print(f"   - 副功率信息: {vice_power_info}")
        else:
            print(f"\n4. 预测结果: {result}")
            
        print(f"\n5. 模型状态信息:")
        print(f"   - 当前阶段: {model.phase}")
        print(f"   - 副功率预测器可用: {hasattr(model, 'vice_power_predictor') and model.vice_power_predictor is not None}")
        if hasattr(model, 'vice_power_shutdown'):
            print(f"   - 副功率是否关闭: {model.vice_power_shutdown}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_with_data()
    if success:
        print(f"\n🎉 所有测试通过!")
    else:
        print(f"\n💥 测试失败!")
