import joblib
import os
import sys
import pickle
from pathlib import Path

def check_model_versions():
    print("=" * 60)
    print("检查预训练模型版本信息")
    print("=" * 60)
    
    # 当前环境信息
    print(f"当前环境信息:")
    print(f"  - Python版本: {sys.version}")
    
    try:
        import sklearn
        print(f"  - sklearn版本: {sklearn.__version__}")
    except ImportError:
        print(f"  - sklearn: 未安装")
    
    try:
        import joblib
        print(f"  - joblib版本: {joblib.__version__}")
    except ImportError:
        print(f"  - joblib: 未安装")
    
    # 检查模型文件
    models_dir = Path("kongwen_power_control/beta_version/v6/production_deployment/models")
    
    print(f"\n模型文件检查:")
    print(f"  - 模型目录: {models_dir}")
    
    model_files = [
        "production_models.joblib",
        "production_models_lj_env_1.joblib",
        "production_model_info.joblib",
        "production_feature_info.joblib"
    ]
    
    for model_file in model_files:
        model_path = models_dir / model_file
        print(f"\n检查文件: {model_file}")
        
        if model_path.exists():
            print(f"  ✅ 文件存在")
            print(f"  📁 文件大小: {model_path.stat().st_size / 1024 / 1024:.2f} MB")
            
            try:
                # 尝试加载模型
                data = joblib.load(model_path)
                print(f"  ✅ joblib加载成功")
                print(f"  📊 数据类型: {type(data)}")
                
                if isinstance(data, dict):
                    print(f"  🔑 字典键: {list(data.keys())}")
                    
                    # 检查是否包含sklearn模型
                    for key, value in data.items():
                        if hasattr(value, '__class__'):
                            class_name = value.__class__.__name__
                            module_name = value.__class__.__module__
                            print(f"    - {key}: {module_name}.{class_name}")
                            
                            # 如果是sklearn模型，尝试获取版本信息
                            if 'sklearn' in module_name:
                                if hasattr(value, '_sklearn_version'):
                                    print(f"      📦 训练时sklearn版本: {value._sklearn_version}")
                                else:
                                    print(f"      ⚠️ 无sklearn版本信息")
                
                elif hasattr(data, '__class__'):
                    class_name = data.__class__.__name__
                    module_name = data.__class__.__module__
                    print(f"  🏷️ 对象类型: {module_name}.{class_name}")
                    
                    if 'sklearn' in module_name and hasattr(data, '_sklearn_version'):
                        print(f"  📦 训练时sklearn版本: {data._sklearn_version}")
                        
            except Exception as e:
                print(f"  ❌ 加载失败: {e}")
                
                # 尝试用pickle加载
                try:
                    with open(model_path, 'rb') as f:
                        data = pickle.load(f)
                    print(f"  ✅ pickle加载成功")
                except Exception as e2:
                    print(f"  ❌ pickle也失败: {e2}")
        else:
            print(f"  ❌ 文件不存在")
    
    # 检查高功率模型
    high_power_dir = models_dir / "high_power_model"
    print(f"\n高功率模型检查:")
    print(f"  - 目录: {high_power_dir}")
    
    if high_power_dir.exists():
        print(f"  ✅ 目录存在")
        high_power_files = list(high_power_dir.glob("*.joblib"))
        print(f"  📁 模型文件数量: {len(high_power_files)}")
        
        for file_path in high_power_files:
            print(f"    - {file_path.name}")
            try:
                data = joblib.load(file_path)
                print(f"      ✅ 加载成功")
                if hasattr(data, '_sklearn_version'):
                    print(f"      📦 sklearn版本: {data._sklearn_version}")
            except Exception as e:
                print(f"      ❌ 加载失败: {e}")
    else:
        print(f"  ❌ 目录不存在")

if __name__ == "__main__":
    check_model_versions()
