# 液口距校准模型 v3版本

## 概述

液口距校准模型v3版本是一个基于温度偏差的液口距校准修正量计算系统，支持优化算法和传统模型双重预测模式。

## 文件结构

```
v3/
├── call.py              # API调用入口
├── model.py             # 核心模型实现
├── models/              # 模型文件目录
│   └── retrained_model_v2.joblib  # 传统模型文件
└── README.md           # 项目说明文档
```

## 核心特性

- **双重预测模式**: 优化算法 + 传统模型回退
- **列表参数支持**: 自动计算平均值，提高数据精度
- **埚位修正功能**: 考虑埚位变化对液口距的影响
- **固定目标温度**: 1449.75°C，工艺标准化
- **完善错误处理**: 任何异常都不会导致程序崩溃
- **高并发支持**: 经过并发测试验证

## API接口

### 请求参数（16个）

#### 优化算法参数（7个）
```json
{
  // 列表格式（5个）- 需计算平均值
  "last_auto_ccd": [1449.5, 1450.0, 1450.5],           // 上次自动定埚位完成后CCD温度
  "last_auto_yekouju": [24.8, 25.0, 25.2],             // 上次自动定埚位完成后绝对液口距
  "last_yinjing_ccd": [1447.5, 1448.0, 1448.5],       // 上次引晶前CCD温度
  "last_yinjing_yekouju": [25.8, 26.0, 26.2],          // 上次引晶前绝对液口距
  "current_auto_ccd": [1451.5, 1452.0, 1452.5],       // 本次自动定埚位完成后CCD温度

  // 单个数值（2个）
  "last_auto_guowei": 100.0,                           // 上次自动定埚位完成后埚位值
  "last_yinjing_guowei": 101.0                         // 上次引晶前埚位值

  // 注意：本次引晶前目标温度使用固定值1449.75°C
}
```

#### 传统参数（9个）- 用于回退
```json
{
  "daoliutong_up": 1.0, "daoliutong_down": 1.0,
  "daoliutong_left": 1.0, "daoliutong_right": 1.0,
  "daoliutong_upleft": 1.0, "daoliutong_upright": 1.0,
  "dingguo_finish_yewen": 1450.0, "dingguo_finish_guowei": 100.0,
  "dingguo_finish_yekouju": 25.0
}
```

### 响应格式
```json
{
  "guowei_adjust": -2.25,
  "version": "v3_optimized"
}
```

## 核心算法

### 1. 埚位修正
```
埚位差值 = 上次引晶前埚位 - 上次自动定埚位后埚位
修正后液口距 = 上次引晶前液口距平均值 + 埚位差值
```

### 2. 比例系数学习
```
温度偏差 = 上次引晶前CCD温度平均值 - 上次自动定埚位后CCD温度平均值
液口距变化 = 修正后液口距 - 上次自动定埚位后液口距平均值
比例系数 r = 液口距变化 / 温度偏差
```

### 3. 预测计算
```
本次温度偏差 = 1449.75 - 本次自动定埚位后CCD温度平均值
液口距修正量 = r × 本次温度偏差
埚位调整量 = -液口距修正量
```

## 使用方法

### 直接调用
```python
from model import YekoujuAdjustModel

model = YekoujuAdjustModel()
result = model.predict(traditional_params, optimization_params)
```

### API调用
```python
from call import predict_yekouju_adjust

result = predict_yekouju_adjust(request)
```

## 安全特性

- **异常处理**: 100%覆盖，任何异常都返回安全默认值0.0
- **数据验证**: 自动过滤NaN、Inf等无效数据
- **范围限制**: 结果自动限制在-10.0到10.0mm范围内
- **安全回退**: 参数不完整时自动回退到传统模型

## 版本信息

**v3.0** - 生产就绪版本
- 基于温度偏差的优化算法
- 列表参数支持和埚位修正
- 固定目标温度1449.75°C
- 完善的错误处理和并发支持