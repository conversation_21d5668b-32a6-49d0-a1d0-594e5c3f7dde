# 液口距校准优化方案 v3版本

## 概述

基于温度偏差的液口距校准修正量计算，解决液口距校准量不准确的问题。

**v3版本特点**：
- 双字典参数接口：传统模式参数字典 + 优化方案参数字典
- 传统模型使用方式完全不变，确保向后兼容
- 优化算法提供清晰的参数接口
- 自动回退机制：优化参数不完整时使用传统模型

## 优化方案

### 1. 数据采集

#### (1) 上次自动定埚位完成后数据
- **时机**: 自动定埚位完成后10-12分钟
- **数据**: 绝对液口距平均值、CCD温度平均值、埚位值平均值
- **目的**: 避免瞬时值波动的影响

#### (2) 上次引晶前数据
- **时机**: 引晶前
- **数据**: CCD温度、绝对液口距、埚位值

#### (3) 本次自动定埚位完成后数据
- **时机**: 本次自动定埚位完成后10-12分钟
- **数据**: 绝对液口距平均值、CCD温度平均值、埚位值平均值

#### (4) 本次引晶前目标温度
- **时机**: 引晶前
- **数据**: 目标温度

### 2. 模型计算

#### 温度偏差-液口距变化标准（线性关系）
```
r = ΔL_last / ΔT_last
```

其中：
- `ΔT_last`: 上次温度偏差（引晶前目标温度 - 自动定埚位完成后CCD温度）
- `ΔL_last`: 上次液口距变化（引晶前绝对液口距 - 自动定埚位完成后绝对液口距）
- `r`: 比例系数 (mm/°C)

#### 修正量计算
```
ΔL_correction = r × ΔT_current
```

其中：
- `ΔT_current`: 本次温度偏差（本次引晶前目标温度 - 本次自动定埚位完成后CCD温度）
- `ΔL_correction`: 液口距校准修正量

### 3. 智能控制逻辑

- **开炉第一次**: 无上一炉数据时使用传统模型
- **人工调整检测**: 检测到人工调整埚位时跳过模型调用
- **数据完整性**: 数据不完整时回退到传统模型
- **异常处理**: 计算异常时返回0（不调整）

## 技术实现

### 核心类

#### `YekoujuAdjustModel`
- 主要模型类
- 双字典参数接口：`predict(traditional_params, optimization_params=None)`
- 集成优化算法和传统模型
- 向后兼容

### API接口

#### 液口距校准接口（双字典参数）
```
POST /yekouju_adjust_test
{
  // === 优化算法参数（推荐使用） ===
  // 上一炉数据（自动定埚位完成后10-12分钟平均值）
  "last_auto_ccd": 1450.0,
  "last_auto_liquid": 25.0,

  // 上一炉数据（引晶前）
  "last_yinjing_target": 1448.0,
  "last_yinjing_liquid": 26.0,

  // 本次数据（自动定埚位完成后10-12分钟平均值）
  "current_auto_ccd": 1452.0,

  // 本次数据（引晶前）
  "target_temp": 1449.0,

  // 控制参数
  "manual_adjusted": false,

  // === 传统参数（用于回退，与v1/v2版本完全一致） ===
  "daoliutong_up": 1.0,
  "daoliutong_down": 1.0,
  "daoliutong_left": 1.0,
  "daoliutong_right": 1.0,
  "daoliutong_upleft": 1.0,
  "daoliutong_upright": 1.0,
  "dingguo_finish_yewen": 1450.0,
  "dingguo_finish_guowei": 100.0,
  "dingguo_finish_yekouju": 25.0
}
```

#### 响应格式
```json
{
  "guowei_adjust": 1.5,
  "version": "v3_optimized"
}
```

## 使用流程

### 生产环境集成

1. **第一炉（建立基准）**:
   - 自动定埚位完成后采集10-12分钟数据
   - 引晶前采集数据
   - 保存到历史数据库

2. **后续炉次（优化计算）**:
   - 自动定埚位完成后采集数据
   - 获取引晶前目标温度
   - 调用优化算法计算修正量
   - 应用修正量

### 异常处理

- **无历史数据**: 自动回退到传统模型
- **人工调整**: 跳过模型调用，避免干扰
- **数据异常**: 返回安全默认值
- **计算错误**: 记录日志并回退

## 测试验证

### 核心算法测试
```bash
python yekouju_adjust/beta_version/v3/test_simple.py
```

### 使用示例
```bash
python yekouju_adjust/beta_version/v3/usage_example.py
```

## 优势特点

1. **智能学习**: 基于历史数据自动学习温度-液口距关系
2. **精确计算**: 使用平均值避免瞬时波动影响
3. **异常处理**: 完善的异常检测和回退机制
4. **向后兼容**: 保持原有接口不变
5. **生产就绪**: 完整的数据管理和状态跟踪

## 部署说明

1. **数据库**: 自动创建SQLite数据库存储历史数据
2. **兼容性**: 兼容现有v2模型，无缝升级
3. **配置**: 无需额外配置，开箱即用
4. **监控**: 内置日志记录，便于问题排查

## 版本信息

- **版本**: v3 (优化版本)
- **创建时间**: 2025-07-28
- **基于**: v2版本模型
- **新增功能**: 温度偏差优化算法
- **兼容性**: 向后兼容v1/v2版本
