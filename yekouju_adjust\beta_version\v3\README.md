# 液口距校准优化方案 v3版本

## 概述

基于温度偏差的液口距校准修正量计算，解决液口距校准量不准确的问题。

## 优化方案

### 1. 数据采集

#### (1) 上次自动定埚位完成后数据
- **时机**: 自动定埚位完成后10-12分钟
- **数据**: 绝对液口距平均值、CCD温度平均值、埚位值平均值
- **目的**: 避免瞬时值波动的影响

#### (2) 上次引晶前数据
- **时机**: 引晶前
- **数据**: CCD温度、绝对液口距、埚位值

#### (3) 本次自动定埚位完成后数据
- **时机**: 本次自动定埚位完成后10-12分钟
- **数据**: 绝对液口距平均值、CCD温度平均值、埚位值平均值

#### (4) 本次引晶前目标温度
- **时机**: 引晶前
- **数据**: 目标温度

### 2. 模型计算

#### 温度偏差-液口距变化标准（线性关系）
```
r = ΔL_last / ΔT_last
```

其中：
- `ΔT_last`: 上次温度偏差（引晶前目标温度 - 自动定埚位完成后CCD温度）
- `ΔL_last`: 上次液口距变化（引晶前绝对液口距 - 自动定埚位完成后绝对液口距）
- `r`: 比例系数 (mm/°C)

#### 修正量计算
```
ΔL_correction = r × ΔT_current
```

其中：
- `ΔT_current`: 本次温度偏差（本次引晶前目标温度 - 本次自动定埚位完成后CCD温度）
- `ΔL_correction`: 液口距校准修正量

### 3. 智能控制逻辑

- **开炉第一次**: 无上一炉数据时使用传统模型
- **人工调整检测**: 检测到人工调整埚位时跳过模型调用
- **数据完整性**: 数据不完整时回退到传统模型
- **异常处理**: 计算异常时返回0（不调整）

## 技术实现

### 核心类

#### `YekoujuHistoryDataManager`
- 历史数据存储和检索
- 设备状态跟踪
- SQLite数据库管理

#### `TemperatureLiquidModel`
- 温度偏差-液口距变化模型
- 比例系数计算
- 修正量预测

#### `YekoujuAdjustModel`
- 主要模型类
- 集成优化算法和传统模型
- 向后兼容

### API接口

#### 1. 保存自动定埚位数据
```
POST /save_auto_dingguo_data
{
  "device_id": "A001",
  "furnace_id": "F20250728001",
  "ccd_temp_list": [1450.2, 1450.1, ...],
  "absolute_liquid_list": [25.1, 25.0, ...],
  "guowei_list": [100.1, 100.0, ...]
}
```

#### 2. 保存引晶前数据
```
POST /save_before_yinjing_data
{
  "device_id": "A001",
  "furnace_id": "F20250728001",
  "ccd_temp": 1448.0,
  "absolute_liquid": 26.0,
  "guowei_value": 101.0,
  "target_temp": 1448.0
}
```

#### 3. 优化液口距校准
```
POST /yekouju_adjust_test
{
  "device_id": "A001",
  "current_auto_ccd": 1452.0,
  "target_temp": 1449.0,
  // 传统参数（用于回退）
  "daoliutong_up": 1.0,
  ...
}
```

#### 4. 标记人工调整
```
POST /mark_manual_adjustment
{
  "device_id": "A001"
}
```

## 使用流程

### 生产环境集成

1. **第一炉（建立基准）**:
   - 自动定埚位完成后采集10-12分钟数据
   - 引晶前采集数据
   - 保存到历史数据库

2. **后续炉次（优化计算）**:
   - 自动定埚位完成后采集数据
   - 获取引晶前目标温度
   - 调用优化算法计算修正量
   - 应用修正量

### 异常处理

- **无历史数据**: 自动回退到传统模型
- **人工调整**: 跳过模型调用，避免干扰
- **数据异常**: 返回安全默认值
- **计算错误**: 记录日志并回退

## 测试验证

### 核心算法测试
```bash
python yekouju_adjust/beta_version/v3/test_simple.py
```

### 使用示例
```bash
python yekouju_adjust/beta_version/v3/usage_example.py
```

## 优势特点

1. **智能学习**: 基于历史数据自动学习温度-液口距关系
2. **精确计算**: 使用平均值避免瞬时波动影响
3. **异常处理**: 完善的异常检测和回退机制
4. **向后兼容**: 保持原有接口不变
5. **生产就绪**: 完整的数据管理和状态跟踪

## 部署说明

1. **数据库**: 自动创建SQLite数据库存储历史数据
2. **兼容性**: 兼容现有v2模型，无缝升级
3. **配置**: 无需额外配置，开箱即用
4. **监控**: 内置日志记录，便于问题排查

## 版本信息

- **版本**: v3 (优化版本)
- **创建时间**: 2025-07-28
- **基于**: v2版本模型
- **新增功能**: 温度偏差优化算法
- **兼容性**: 向后兼容v1/v2版本
