"""
液口距校准模型 v3版本 - 优化版本
基于温度偏差的液口距校准修正量计算
简化版本：参考v1结构，所有数据通过call.py传入predict方法
创建时间: 2025-07-28
版本: v3
"""

from joblib import load
import os
import numpy as np

# 传统模型需要的特征（用于回退）
FEATURE = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left', 'daoliutong_right',
           'daoliutong_upleft','daoliutong_upright', 'dingguo_finish_yewen',
           'dingguo_finish_guowei','dingguo_finish_yekouju']

class YekoujuAdjustModel:
    """
    液口距校准模型 v3版本 - 优化版本

    特点:
    - 基于温度偏差的液口距校准修正量计算
    - 简化设计，所有数据通过call.py传入predict方法
    - 向后兼容传统模型预测
    """

    # 固定的引晶前目标温度
    YINJING_TARGET_TEMP = 1449.75  # °C

    def __init__(self, model_path=None):
        # 加载传统模型（用于回退）
        if model_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, 'models', 'retrained_model_v2.joblib')
            model_path = os.path.normpath(model_path)

        print(f"液口距校准模型 v3版本初始化")
        print(f"模型路径: {model_path}")
        print(f"版本: v3 (优化版本)")
        print(f"创建时间: 2025-07-28")

        try:
            self.traditional_model = load(model_path)
            print(f"✅ 传统模型加载成功（用于回退）")
        except Exception as e:
            print(f"⚠️ 传统模型加载失败，将仅使用优化算法: {e}")
            self.traditional_model = None

    def predict(self, x, optimization_params=None):
        """
        进行液口距校准预测（增强错误处理版本）

        参数:
            x (dict): 传统模型参数字典，包含：
                daoliutong_up, daoliutong_down, daoliutong_left, daoliutong_right,
                daoliutong_upleft, daoliutong_upright, dingguo_finish_yewen,
                dingguo_finish_guowei, dingguo_finish_yekouju

            optimization_params (dict, optional): 优化算法参数字典，包含：
                last_auto_ccd: 上次自动定埚位完成后CCD温度列表（需计算平均值）
                last_auto_yekouju: 上次自动定埚位完成后绝对液口距列表（需计算平均值）
                last_auto_guowei: 上次自动定埚位完成后埚位值（单个数值）
                last_yinjing_ccd: 上次引晶前CCD温度列表（需计算平均值）
                last_yinjing_yekouju: 上次引晶前绝对液口距列表（需计算平均值）
                last_yinjing_guowei: 上次引晶前埚位值（单个数值）
                current_auto_ccd: 本次自动定埚位完成后CCD温度列表（需计算平均值）
                注意：本次引晶前目标温度使用固定值1449.75°C

        返回:
            预测的液口距调整值 (float)，异常情况下返回0.0
        """
        print(f"🔍 开始预测 - 优化参数: {'有' if optimization_params else '无'}")

        try:
            # 输入参数验证
            if x is None:
                print("⚠️ 传统参数x为None，使用空字典")
                x = {}

            if not isinstance(x, dict):
                print(f"⚠️ 传统参数x类型错误: {type(x)}，转换为字典")
                x = {}

            # 检查是否使用优化算法
            if optimization_params and self._has_optimization_data(optimization_params):
                print("✅ 使用优化算法进行预测")
                result = self._calculate_optimized_correction(optimization_params)
                print(f"🎯 优化算法预测结果: {result}mm")
                return result
            else:
                print("⚠️ 优化参数不完整，回退到传统模型")
                result = self._predict_with_traditional_model(x)
                print(f"🎯 传统模型预测结果: {result}mm")
                return result

        except Exception as e:
            print(f"❌ 预测过程发生异常: {type(e).__name__}: {e}")
            print("🔄 返回安全默认值: 0.0mm")
            return 0.0

    def _has_optimization_data(self, optimization_params):
        """检查是否包含优化算法所需的完整数据（增强验证版本）"""
        try:
            if not optimization_params:
                print("📋 优化参数为空")
                return False

            if not isinstance(optimization_params, dict):
                print(f"📋 优化参数类型错误: {type(optimization_params)}")
                return False

            required_keys = [
                'last_auto_ccd', 'last_auto_yekouju', 'last_auto_guowei',
                'last_yinjing_ccd', 'last_yinjing_yekouju', 'last_yinjing_guowei',
                'current_auto_ccd'
            ]

            missing_keys = []
            invalid_keys = []

            for key in required_keys:
                if key not in optimization_params:
                    missing_keys.append(key)
                elif optimization_params[key] is None:
                    invalid_keys.append(f"{key}=None")
                elif key in ['last_auto_guowei', 'last_yinjing_guowei']:
                    # 单个数值参数验证
                    try:
                        float(optimization_params[key])
                    except (ValueError, TypeError):
                        invalid_keys.append(f"{key}=无效数值")
                else:
                    # 列表参数验证
                    if not isinstance(optimization_params[key], list):
                        invalid_keys.append(f"{key}=非列表")
                    elif len(optimization_params[key]) == 0:
                        invalid_keys.append(f"{key}=空列表")

            if missing_keys:
                print(f"📋 缺少必需参数: {missing_keys}")
            if invalid_keys:
                print(f"📋 无效参数: {invalid_keys}")

            is_valid = len(missing_keys) == 0 and len(invalid_keys) == 0
            print(f"📋 优化参数验证: {'✅ 通过' if is_valid else '❌ 失败'}")
            return is_valid

        except Exception as e:
            print(f"📋 参数验证异常: {e}")
            return False

    def _calculate_list_average(self, data_list, param_name):
        """
        计算列表数据的平均值（增强错误处理版本）

        参数:
            data_list: 数据列表
            param_name: 参数名称（用于日志）

        返回:
            平均值 (float)
        """
        try:
            # 类型验证
            if not isinstance(data_list, list):
                raise ValueError(f"{param_name} 必须是列表类型，当前类型: {type(data_list)}")

            if len(data_list) == 0:
                raise ValueError(f"{param_name} 必须是非空列表")

            # 数据转换和验证
            numeric_data = []
            invalid_count = 0

            for i, value in enumerate(data_list):
                try:
                    # 尝试转换为浮点数
                    num_value = float(value)

                    # 检查是否为有限数值
                    if np.isfinite(num_value):
                        # 合理性检查（温度和液口距的合理范围）
                        if param_name.endswith('_ccd') and not (1000 <= num_value <= 2000):
                            print(f"⚠️ {param_name}[{i}]={num_value} 超出温度合理范围[1000-2000]°C")
                        elif param_name.endswith('_yekouju') and not (0 <= num_value <= 100):
                            print(f"⚠️ {param_name}[{i}]={num_value} 超出液口距合理范围[0-100]mm")

                        numeric_data.append(num_value)
                    else:
                        invalid_count += 1
                        print(f"⚠️ {param_name}[{i}]={value} 为无效数值(NaN/Inf)")

                except (ValueError, TypeError):
                    invalid_count += 1
                    print(f"⚠️ {param_name}[{i}]={value} 无法转换为数值")

            # 检查有效数据
            if len(numeric_data) == 0:
                raise ValueError(f"{param_name} 列表中没有有效数据（原始{len(data_list)}个，无效{invalid_count}个）")

            if invalid_count > 0:
                print(f"📊 {param_name}: 原始{len(data_list)}个数据，有效{len(numeric_data)}个，无效{invalid_count}个")

            # 计算平均值
            avg_value = float(np.mean(numeric_data))

            # 输出详细信息
            if len(numeric_data) == len(data_list):
                print(f"  {param_name}: {data_list} → 平均值: {avg_value:.2f}")
            else:
                print(f"  {param_name}: {data_list} → 有效数据: {numeric_data} → 平均值: {avg_value:.2f}")

            return avg_value

        except Exception as e:
            print(f"❌ 计算 {param_name} 平均值失败: {e}")
            raise

    def _calculate_optimized_correction(self, optimization_params):
        """使用优化算法计算液口距校准修正量（支持列表格式和埚位修正）"""
        try:
            print("开始计算列表数据平均值:")

            # 计算列表数据的平均值
            last_auto_ccd_avg = self._calculate_list_average(
                optimization_params['last_auto_ccd'], 'last_auto_ccd')
            last_auto_yekouju_avg = self._calculate_list_average(
                optimization_params['last_auto_yekouju'], 'last_auto_yekouju')
            last_yinjing_ccd_avg = self._calculate_list_average(
                optimization_params['last_yinjing_ccd'], 'last_yinjing_ccd')
            last_yinjing_yekouju_avg = self._calculate_list_average(
                optimization_params['last_yinjing_yekouju'], 'last_yinjing_yekouju')
            current_auto_ccd_avg = self._calculate_list_average(
                optimization_params['current_auto_ccd'], 'current_auto_ccd')

            # 获取和验证单个数值参数
            try:
                last_auto_guowei = float(optimization_params['last_auto_guowei'])
                if not np.isfinite(last_auto_guowei):
                    raise ValueError("last_auto_guowei 不是有限数值")
                if not (50 <= last_auto_guowei <= 150):
                    print(f"⚠️ last_auto_guowei={last_auto_guowei} 超出合理范围[50-150]mm")
            except (ValueError, TypeError) as e:
                raise ValueError(f"last_auto_guowei 无效: {e}")

            try:
                last_yinjing_guowei = float(optimization_params['last_yinjing_guowei'])
                if not np.isfinite(last_yinjing_guowei):
                    raise ValueError("last_yinjing_guowei 不是有限数值")
                if not (50 <= last_yinjing_guowei <= 150):
                    print(f"⚠️ last_yinjing_guowei={last_yinjing_guowei} 超出合理范围[50-150]mm")
            except (ValueError, TypeError) as e:
                raise ValueError(f"last_yinjing_guowei 无效: {e}")

            # 使用固定的引晶前目标温度
            current_yinjing_target = self.YINJING_TARGET_TEMP

            print(f"  last_auto_guowei: {last_auto_guowei} (单个数值)")
            print(f"  last_yinjing_guowei: {last_yinjing_guowei} (单个数值)")
            print(f"  current_yinjing_target: {current_yinjing_target} (固定值)")

            # 计算埚位差值（增强验证）
            guowei_diff = last_yinjing_guowei - last_auto_guowei
            print(f"🔧 埚位修正计算:")
            print(f"  埚位差值: {last_yinjing_guowei} - {last_auto_guowei} = {guowei_diff:.2f}mm")

            # 埚位差值合理性检查
            if abs(guowei_diff) > 10:
                print(f"⚠️ 埚位差值过大: {guowei_diff:.2f}mm，可能存在异常")

            # 修正上次引晶前绝对液口距
            adjusted_last_yinjing_yekouju = last_yinjing_yekouju_avg + guowei_diff
            print(f"  修正后液口距: {last_yinjing_yekouju_avg:.2f} + {guowei_diff:.2f} = {adjusted_last_yinjing_yekouju:.2f}mm")

            # 计算上次温度偏差和液口距偏差（使用修正后的液口距）
            delta_t_last = last_yinjing_ccd_avg - last_auto_ccd_avg
            delta_l_last = adjusted_last_yinjing_yekouju - last_auto_yekouju_avg

            print(f"📈 比例系数计算:")
            print(f"  上次温度偏差: {last_yinjing_ccd_avg:.2f} - {last_auto_ccd_avg:.2f} = {delta_t_last:.2f}°C")
            print(f"  上次液口距变化: {adjusted_last_yinjing_yekouju:.2f} - {last_auto_yekouju_avg:.2f} = {delta_l_last:.2f}mm")

            # 温度偏差合理性检查
            if abs(delta_t_last) > 50:
                print(f"⚠️ 温度偏差过大: {delta_t_last:.2f}°C，可能存在异常")

            # 液口距变化合理性检查
            if abs(delta_l_last) > 20:
                print(f"⚠️ 液口距变化过大: {delta_l_last:.2f}mm，可能存在异常")

            # 计算比例系数 r = ΔL_last / ΔT_last（增强除零保护）
            if abs(delta_t_last) < 0.1:  # 温度变化太小，使用默认值
                r = 0.1
                print(f"  温度变化太小({delta_t_last:.2f}°C)，使用默认比例系数: {r}")
            else:
                try:
                    r = delta_l_last / delta_t_last
                    r_original = r

                    # 检查比例系数是否为有限数值
                    if not np.isfinite(r):
                        print(f"⚠️ 比例系数计算结果无效: {r}，使用默认值0.1")
                        r = 0.1
                    else:
                        # 限制比例系数的合理范围
                        r = np.clip(r, -2.0, 2.0)  # 扩大范围以适应更多情况
                        if abs(r_original - r) > 0.01:
                            print(f"  原始比例系数: {r_original:.4f} → 限制后: {r:.4f} mm/°C")
                        else:
                            print(f"  比例系数: {r:.4f} mm/°C")

                except Exception as e:
                    print(f"⚠️ 比例系数计算异常: {e}，使用默认值0.1")
                    r = 0.1

            # 计算本次温度偏差（增强验证）
            delta_t_current = current_yinjing_target - current_auto_ccd_avg
            print(f"🎯 本次预测:")
            print(f"  本次温度偏差: {current_yinjing_target} - {current_auto_ccd_avg:.2f} = {delta_t_current:.2f}°C")

            # 本次温度偏差合理性检查
            if abs(delta_t_current) > 50:
                print(f"⚠️ 本次温度偏差过大: {delta_t_current:.2f}°C，可能存在异常")

            # 计算液口距修正量 ΔL_correction = r × ΔT_current（增强验证）
            try:
                liquid_correction = r * delta_t_current

                # 检查计算结果是否为有限数值
                if not np.isfinite(liquid_correction):
                    print(f"⚠️ 液口距修正量计算结果无效: {liquid_correction}，使用默认值0")
                    liquid_correction = 0.0
                else:
                    print(f"  液口距修正量: {r:.4f} × {delta_t_current:.2f} = {liquid_correction:.2f}mm")

                    # 液口距修正量合理性检查
                    if abs(liquid_correction) > 10:
                        print(f"⚠️ 液口距修正量过大: {liquid_correction:.2f}mm")

            except Exception as e:
                print(f"⚠️ 液口距修正量计算异常: {e}，使用默认值0")
                liquid_correction = 0.0

            # 转换为埚位调整量（埚位与液口距方向相反）
            try:
                guowei_adjust = -liquid_correction
                print(f"  埚位调整量: -{liquid_correction:.2f} = {guowei_adjust:.2f}mm")

                # 检查结果是否为有限数值
                if not np.isfinite(guowei_adjust):
                    print(f"⚠️ 埚位调整量无效: {guowei_adjust}，使用默认值0")
                    guowei_adjust = 0.0

            except Exception as e:
                print(f"⚠️ 埚位调整量计算异常: {e}，使用默认值0")
                guowei_adjust = 0.0

            # 限制埚位调整量的合理范围（增强验证）
            guowei_adjust_original = guowei_adjust
            guowei_adjust = np.clip(guowei_adjust, -10.0, 10.0)  # 扩大安全范围

            if abs(guowei_adjust_original - guowei_adjust) > 0.01:
                print(f"  埚位调整量限制: {guowei_adjust_original:.2f} → {guowei_adjust:.2f}mm")

            # 最终结果验证
            try:
                final_result = round(guowei_adjust, 2)
                if not np.isfinite(final_result):
                    print(f"⚠️ 最终结果无效: {final_result}，使用默认值0")
                    final_result = 0.0

                print(f"✅ 优化算法计算完成: 埚位调整量={final_result:.2f}mm")
                return final_result

            except Exception as e:
                print(f"⚠️ 最终结果处理异常: {e}，返回默认值0")
                return 0.0

        except Exception as e:
            print(f"优化算法计算失败: {e}")
            return 0.0

    def _predict_with_traditional_model(self, x):
        """使用传统模型进行预测（增强错误处理版本）"""
        print("🔄 尝试使用传统模型进行预测")

        try:
            if self.traditional_model is None:
                print("❌ 传统模型不可用，返回默认值0")
                return 0.0

            # 输入参数验证
            if not isinstance(x, dict):
                print(f"❌ 传统模型输入参数类型错误: {type(x)}，返回默认值0")
                return 0.0

            # 检查传统模型所需的特征是否完整
            missing_features = []
            invalid_features = []

            for feature in FEATURE:
                if feature not in x:
                    missing_features.append(feature)
                elif x[feature] is None:
                    invalid_features.append(f"{feature}=None")
                else:
                    # 验证数值类型
                    try:
                        value = float(x[feature])
                        if not np.isfinite(value):
                            invalid_features.append(f"{feature}=无效数值")
                    except (ValueError, TypeError):
                        invalid_features.append(f"{feature}=无法转换为数值")

            if missing_features:
                print(f"❌ 传统模型缺少特征: {missing_features}，返回默认值0")
                return 0.0

            if invalid_features:
                print(f"❌ 传统模型特征无效: {invalid_features}，返回默认值0")
                return 0.0

            # 按照指定顺序提取特征并转换为数值
            try:
                x_values = []
                for feature in FEATURE:
                    value = float(x[feature])
                    x_values.append(value)

                print(f"📊 传统模型输入特征: {dict(zip(FEATURE, x_values))}")

            except Exception as e:
                print(f"❌ 特征提取失败: {e}，返回默认值0")
                return 0.0

            # 进行预测
            try:
                prediction_array = self.traditional_model.predict([x_values])
                prediction = float(prediction_array[0])

                # 验证预测结果
                if not np.isfinite(prediction):
                    print(f"❌ 传统模型预测结果无效: {prediction}，返回默认值0")
                    return 0.0

                # 合理性检查
                if abs(prediction) > 20:
                    print(f"⚠️ 传统模型预测结果过大: {prediction}mm")

                print(f"✅ 传统模型预测成功: {prediction:.2f}mm")
                return round(prediction, 2)

            except Exception as e:
                print(f"❌ 传统模型预测执行失败: {e}，返回默认值0")
                return 0.0

        except Exception as e:
            print(f"❌ 传统模型预测过程异常: {type(e).__name__}: {e}，返回默认值0")
            return 0.0
