"""
液口距校准模型 v3版本 - 优化版本
基于温度偏差的液口距校准修正量计算
简化版本：参考v1结构，所有数据通过call.py传入predict方法
创建时间: 2025-07-28
版本: v3
"""

from joblib import load
import os
import numpy as np

# 传统模型需要的特征（用于回退）
FEATURE = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left', 'daoliutong_right',
           'daoliutong_upleft','daoliutong_upright', 'dingguo_finish_yewen',
           'dingguo_finish_guowei','dingguo_finish_yekouju']

class YekoujuAdjustModel:
    """
    液口距校准模型 v3版本 - 优化版本

    特点:
    - 基于温度偏差的液口距校准修正量计算
    - 简化设计，所有数据通过call.py传入predict方法
    - 向后兼容传统模型预测
    """

    def __init__(self, model_path=None):
        # 加载传统模型（用于回退）
        if model_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, 'models', 'retrained_model_v2.joblib')
            model_path = os.path.normpath(model_path)

        print(f"液口距校准模型 v3版本初始化")
        print(f"模型路径: {model_path}")
        print(f"版本: v3 (优化版本)")
        print(f"创建时间: 2025-07-28")

        try:
            self.traditional_model = load(model_path)
            print(f"✅ 传统模型加载成功（用于回退）")
        except Exception as e:
            print(f"⚠️ 传统模型加载失败，将仅使用优化算法: {e}")
            self.traditional_model = None

    def predict(self, x, optimization_params=None):
        """
        进行液口距校准预测

        参数:
            x (dict): 传统模型参数字典，包含：
                daoliutong_up, daoliutong_down, daoliutong_left, daoliutong_right,
                daoliutong_upleft, daoliutong_upright, dingguo_finish_yewen,
                dingguo_finish_guowei, dingguo_finish_yekouju

            optimization_params (dict, optional): 优化算法参数字典，包含：
                last_auto_ccd: 上次自动定埚位完成后CCD温度列表（需计算平均值）
                last_auto_yekouju: 上次自动定埚位完成后绝对液口距列表（需计算平均值）
                last_auto_guowei: 上次自动定埚位完成后埚位值（单个数值）
                last_yinjing_ccd: 上次引晶前CCD温度列表（需计算平均值）
                last_yinjing_yekouju: 上次引晶前绝对液口距列表（需计算平均值）
                last_yinjing_guowei: 上次引晶前埚位值（单个数值）
                current_auto_ccd: 本次自动定埚位完成后CCD温度列表（需计算平均值）
                current_yinjing_target: 本次引晶前目标温度（单个数值）

        返回:
            预测的液口距调整值 (float)
        """
        try:
            # 检查是否使用优化算法
            if optimization_params and self._has_optimization_data(optimization_params):
                return self._calculate_optimized_correction(optimization_params)
            else:
                # 回退到传统模型
                return self._predict_with_traditional_model(x)

        except Exception as e:
            print(f"预测失败: {e}")
            return 0.0

    def _has_optimization_data(self, optimization_params):
        """检查是否包含优化算法所需的完整数据"""
        if not optimization_params:
            return False

        required_keys = [
            'last_auto_ccd', 'last_auto_yekouju', 'last_auto_guowei',
            'last_yinjing_ccd', 'last_yinjing_yekouju', 'last_yinjing_guowei',
            'current_auto_ccd', 'current_yinjing_target'
        ]
        return all(key in optimization_params and optimization_params[key] is not None for key in required_keys)

    def _calculate_list_average(self, data_list, param_name):
        """
        计算列表数据的平均值

        参数:
            data_list: 数据列表
            param_name: 参数名称（用于日志）

        返回:
            平均值 (float)
        """
        try:
            if not isinstance(data_list, list) or len(data_list) == 0:
                raise ValueError(f"{param_name} 必须是非空列表")

            # 转换为numpy数组并去除无效值
            data_array = np.array(data_list, dtype=float)
            valid_data = data_array[~np.isnan(data_array)]

            if len(valid_data) == 0:
                raise ValueError(f"{param_name} 列表中没有有效数据")

            avg_value = float(np.mean(valid_data))
            print(f"  {param_name}: {data_list} → 平均值: {avg_value:.2f}")

            return avg_value

        except Exception as e:
            print(f"计算 {param_name} 平均值失败: {e}")
            raise

    def _calculate_optimized_correction(self, optimization_params):
        """使用优化算法计算液口距校准修正量（支持列表格式和埚位修正）"""
        try:
            print("📊 开始计算列表数据平均值:")

            # 计算列表数据的平均值
            last_auto_ccd_avg = self._calculate_list_average(
                optimization_params['last_auto_ccd'], 'last_auto_ccd')
            last_auto_yekouju_avg = self._calculate_list_average(
                optimization_params['last_auto_yekouju'], 'last_auto_yekouju')
            last_yinjing_ccd_avg = self._calculate_list_average(
                optimization_params['last_yinjing_ccd'], 'last_yinjing_ccd')
            last_yinjing_yekouju_avg = self._calculate_list_average(
                optimization_params['last_yinjing_yekouju'], 'last_yinjing_yekouju')
            current_auto_ccd_avg = self._calculate_list_average(
                optimization_params['current_auto_ccd'], 'current_auto_ccd')

            # 获取单个数值参数
            last_auto_guowei = optimization_params['last_auto_guowei']
            last_yinjing_guowei = optimization_params['last_yinjing_guowei']
            current_yinjing_target = optimization_params['current_yinjing_target']

            print(f"  last_auto_guowei: {last_auto_guowei} (单个数值)")
            print(f"  last_yinjing_guowei: {last_yinjing_guowei} (单个数值)")
            print(f"  current_yinjing_target: {current_yinjing_target} (单个数值)")

            # 计算埚位差值
            guowei_diff = last_yinjing_guowei - last_auto_guowei
            print(f"\n🔧 埚位修正计算:")
            print(f"  埚位差值: {last_yinjing_guowei} - {last_auto_guowei} = {guowei_diff:.2f}mm")

            # 修正上次引晶前绝对液口距
            adjusted_last_yinjing_yekouju = last_yinjing_yekouju_avg + guowei_diff
            print(f"  修正后液口距: {last_yinjing_yekouju_avg:.2f} + {guowei_diff:.2f} = {adjusted_last_yinjing_yekouju:.2f}mm")

            # 计算上次温度偏差和液口距偏差（使用修正后的液口距）
            delta_t_last = last_yinjing_ccd_avg - last_auto_ccd_avg
            delta_l_last = adjusted_last_yinjing_yekouju - last_auto_yekouju_avg

            print(f"\n📈 比例系数计算:")
            print(f"  上次温度偏差: {last_yinjing_ccd_avg:.2f} - {last_auto_ccd_avg:.2f} = {delta_t_last:.2f}°C")
            print(f"  上次液口距变化: {adjusted_last_yinjing_yekouju:.2f} - {last_auto_yekouju_avg:.2f} = {delta_l_last:.2f}mm")

            # 计算比例系数 r = ΔL_last / ΔT_last
            if abs(delta_t_last) < 0.1:  # 温度变化太小，使用默认值
                r = 0.1
                print(f"  温度变化太小({delta_t_last:.2f}°C)，使用默认比例系数: {r}")
            else:
                r = delta_l_last / delta_t_last
                r_original = r
                # 限制比例系数的合理范围
                r = np.clip(r, -1.0, 1.0)
                print(f"  原始比例系数: {r_original:.4f} → 限制后: {r:.4f} mm/°C")

            # 计算本次温度偏差
            delta_t_current = current_yinjing_target - current_auto_ccd_avg
            print(f"\n🎯 本次预测:")
            print(f"  本次温度偏差: {current_yinjing_target} - {current_auto_ccd_avg:.2f} = {delta_t_current:.2f}°C")

            # 计算液口距修正量 ΔL_correction = r × ΔT_current
            liquid_correction = r * delta_t_current
            print(f"  液口距修正量: {r:.4f} × {delta_t_current:.2f} = {liquid_correction:.2f}mm")

            # 转换为埚位调整量（埚位与液口距方向相反）
            guowei_adjust = -liquid_correction
            print(f"  埚位调整量: -{liquid_correction:.2f} = {guowei_adjust:.2f}mm")

            # 限制埚位调整量的合理范围
            guowei_adjust_original = guowei_adjust
            guowei_adjust = np.clip(guowei_adjust, -5.0, 5.0)
            if abs(guowei_adjust_original - guowei_adjust) > 0.01:
                print(f"  限制后埚位调整量: {guowei_adjust:.2f}mm")

            print(f"\n✅ 优化算法计算完成: 埚位调整量={guowei_adjust:.2f}mm")

            return round(guowei_adjust, 2)

        except Exception as e:
            print(f"优化算法计算失败: {e}")
            return 0.0

    def _predict_with_traditional_model(self, x):
        """使用传统模型进行预测"""
        try:
            if self.traditional_model is None:
                print("传统模型不可用，返回默认值0")
                return 0.0

            # 检查传统模型所需的特征是否完整
            missing_features = [f for f in FEATURE if f not in x or x[f] is None]
            if missing_features:
                print(f"传统模型缺少特征: {missing_features}，返回默认值0")
                return 0.0

            # 按照指定顺序提取特征（参考v1版本的实现）
            x_columns = list(map(lambda v: v, FEATURE))
            x_values = list(map(lambda f: x[f], x_columns))

            # 进行预测
            prediction = self.traditional_model.predict([x_values])[0]
            print(f"✅ 使用传统模型预测: {prediction}")

            return prediction

        except Exception as e:
            print(f"传统模型预测失败: {e}")
            return 0.0
