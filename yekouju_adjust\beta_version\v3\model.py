"""
液口距校准模型 v3版本 - 优化版本
基于温度偏差的液口距校准修正量计算
实现方案：
1. 数据采集：自动定埚位完成后10-12min的平均值、引晶前数据
2. 模型计算：构建温度偏差-液口距变化标准，推算修正量
3. 智能控制：检测人工调整、开炉第一次处理等
创建时间: 2025-07-28
版本: v3
"""

from joblib import load
import os
import sqlite3
import numpy as np
from datetime import datetime, timedelta
import logging

# 传统模型需要的特征（用于无历史数据时的回退）
FEATURE = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left', 'daoliutong_right', 
           'daoliutong_upleft','daoliutong_upright', 'dingguo_finish_yewen', 
           'dingguo_finish_guowei','dingguo_finish_yekouju']

class YekoujuHistoryDataManager:
    """
    液口距历史数据管理器
    负责存储和检索上一炉的数据
    """
    
    def __init__(self, db_path=None):
        if db_path is None:
            # Windows环境使用临时目录
            import tempfile
            self.db_path = os.path.join(tempfile.gettempdir(), "yekouju_history.sqlite")
        else:
            self.db_path = db_path
        self._init_database()
        
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS yekouju_history (
                        device_id TEXT,
                        furnace_id TEXT,
                        data_type TEXT,  -- 'auto_dingguo_after' 或 'before_yinjing'
                        timestamp TEXT,
                        ccd_temp REAL,
                        absolute_liquid REAL,
                        guowei_value REAL,
                        target_temp REAL,
                        is_manual_adjusted INTEGER DEFAULT 0,  -- 是否人工调整过埚位
                        data_count INTEGER DEFAULT 1,  -- 数据点数量
                        PRIMARY KEY (device_id, furnace_id, data_type)
                    )
                """)
                
                # 创建状态跟踪表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS yekouju_status (
                        device_id TEXT PRIMARY KEY,
                        current_furnace_id TEXT,
                        auto_dingguo_completed INTEGER DEFAULT 0,
                        auto_dingguo_time TEXT,
                        manual_guowei_adjusted INTEGER DEFAULT 0,
                        last_update TEXT
                    )
                """)
                conn.commit()
        except Exception as e:
            logging.error(f"数据库初始化失败: {e}")
            
    def save_data(self, device_id, furnace_id, data_type, ccd_temp, absolute_liquid, 
                  guowei_value, target_temp=None, is_manual_adjusted=False, data_count=1):
        """保存历史数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO yekouju_history 
                    (device_id, furnace_id, data_type, timestamp, ccd_temp, 
                     absolute_liquid, guowei_value, target_temp, is_manual_adjusted, data_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (device_id, furnace_id, data_type, datetime.now().isoformat(),
                      ccd_temp, absolute_liquid, guowei_value, target_temp, 
                      1 if is_manual_adjusted else 0, data_count))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"保存历史数据失败: {e}")
            return False
            
    def update_status(self, device_id, furnace_id=None, auto_dingguo_completed=None, 
                     manual_guowei_adjusted=None):
        """更新设备状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取当前状态
                cursor = conn.execute("""
                    SELECT current_furnace_id, auto_dingguo_completed, manual_guowei_adjusted
                    FROM yekouju_status WHERE device_id = ?
                """, (device_id,))
                
                current_data = cursor.fetchone()
                
                if current_data:
                    current_furnace, current_auto, current_manual = current_data
                    # 更新状态
                    new_furnace = furnace_id if furnace_id is not None else current_furnace
                    new_auto = auto_dingguo_completed if auto_dingguo_completed is not None else current_auto
                    new_manual = manual_guowei_adjusted if manual_guowei_adjusted is not None else current_manual
                    
                    conn.execute("""
                        UPDATE yekouju_status 
                        SET current_furnace_id = ?, auto_dingguo_completed = ?, 
                            manual_guowei_adjusted = ?, last_update = ?
                        WHERE device_id = ?
                    """, (new_furnace, new_auto, new_manual, datetime.now().isoformat(), device_id))
                else:
                    # 插入新状态
                    conn.execute("""
                        INSERT INTO yekouju_status 
                        (device_id, current_furnace_id, auto_dingguo_completed, 
                         manual_guowei_adjusted, last_update)
                        VALUES (?, ?, ?, ?, ?)
                    """, (device_id, furnace_id or '', 
                          auto_dingguo_completed or 0, manual_guowei_adjusted or 0,
                          datetime.now().isoformat()))
                
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"更新状态失败: {e}")
            return False
            
    def get_status(self, device_id):
        """获取设备状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT current_furnace_id, auto_dingguo_completed, 
                           manual_guowei_adjusted, auto_dingguo_time
                    FROM yekouju_status WHERE device_id = ?
                """, (device_id,))
                
                result = cursor.fetchone()
                if result:
                    return {
                        'current_furnace_id': result[0],
                        'auto_dingguo_completed': bool(result[1]),
                        'manual_guowei_adjusted': bool(result[2]),
                        'auto_dingguo_time': result[3]
                    }
                return None
        except Exception as e:
            logging.error(f"获取状态失败: {e}")
            return None
            
    def get_last_furnace_data(self, device_id):
        """获取上一炉的数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT furnace_id, data_type, ccd_temp, absolute_liquid, 
                           guowei_value, target_temp, is_manual_adjusted
                    FROM yekouju_history 
                    WHERE device_id = ? 
                    ORDER BY timestamp DESC
                    LIMIT 10
                """, (device_id,))
                
                rows = cursor.fetchall()
                if not rows:
                    return None
                    
                # 组织数据
                last_data = {}
                for row in rows:
                    furnace_id, data_type, ccd_temp, absolute_liquid, guowei_value, target_temp, is_manual_adjusted = row
                    if furnace_id not in last_data:
                        last_data[furnace_id] = {}
                    last_data[furnace_id][data_type] = {
                        'ccd_temp': ccd_temp,
                        'absolute_liquid': absolute_liquid,
                        'guowei_value': guowei_value,
                        'target_temp': target_temp,
                        'is_manual_adjusted': bool(is_manual_adjusted)
                    }
                
                # 返回最新炉次的完整数据
                if last_data:
                    latest_furnace = list(last_data.keys())[0]
                    return last_data[latest_furnace]
                return None
                
        except Exception as e:
            logging.error(f"获取历史数据失败: {e}")
            return None

class TemperatureLiquidModel:
    """
    温度偏差-液口距变化模型
    实现线性关系：r = ΔL_last / ΔT_last
    """
    
    def __init__(self, default_r=0.1):
        """
        初始化模型
        
        参数:
            default_r: 默认比例系数 (mm/°C)
        """
        self.default_r = default_r
        
    def calculate_ratio(self, delta_t_last, delta_l_last):
        """
        计算温度偏差-液口距变化比例系数
        
        参数:
            delta_t_last: 上次温度偏差 (°C)
            delta_l_last: 上次液口距变化 (mm)
            
        返回:
            r: 比例系数 (mm/°C)
        """
        try:
            if abs(delta_t_last) < 0.1:  # 温度变化太小，使用默认值
                return self.default_r
                
            r = delta_l_last / delta_t_last
            
            # 限制比例系数的合理范围
            r = np.clip(r, -1.0, 1.0)
            
            return r
            
        except Exception as e:
            logging.error(f"计算比例系数失败: {e}")
            return self.default_r
            
    def predict_correction(self, r, delta_t_current):
        """
        预测液口距校准修正量
        
        参数:
            r: 比例系数 (mm/°C)
            delta_t_current: 当前温度偏差 (°C)
            
        返回:
            correction: 修正量 (mm)
        """
        try:
            correction = r * delta_t_current
            
            # 限制修正量的合理范围
            correction = np.clip(correction, -5.0, 5.0)
            
            return correction
            
        except Exception as e:
            logging.error(f"计算修正量失败: {e}")
            return 0.0

class YekoujuAdjustModel:
    """
    液口距校准模型 v3版本 - 优化版本
    
    特点:
    - 基于温度偏差的液口距校准修正量计算
    - 支持历史数据管理和状态跟踪
    - 智能检测人工调整和开炉第一次情况
    - 向后兼容传统模型预测
    """
    
    def __init__(self, model_path=None):
        # 初始化历史数据管理器
        self.history_manager = YekoujuHistoryDataManager()
        
        # 初始化温度-液口距模型
        self.temp_liquid_model = TemperatureLiquidModel()
        
        # 加载传统模型（用于回退）
        if model_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, 'models', 'retrained_model_v2.joblib')
            model_path = os.path.normpath(model_path)

        print(f"液口距校准模型 v3版本初始化")
        print(f"模型路径: {model_path}")
        print(f"版本: v3 (优化版本)")
        print(f"创建时间: 2025-07-28")

        try:
            self.traditional_model = load(model_path)
            print(f"✅ 传统模型加载成功（用于回退）")
        except Exception as e:
            print(f"⚠️ 传统模型加载失败，将仅使用优化算法: {e}")
            self.traditional_model = None

    def save_auto_dingguo_data(self, device_id, furnace_id, ccd_temp_avg,
                              absolute_liquid_avg, guowei_avg, data_count):
        """
        保存自动定埚位完成后10-12分钟的平均数据

        参数:
            device_id: 设备ID
            furnace_id: 炉次ID
            ccd_temp_avg: CCD温度平均值
            absolute_liquid_avg: 绝对液口距平均值
            guowei_avg: 埚位值平均值
            data_count: 数据点数量
        """
        return self.history_manager.save_data(
            device_id, furnace_id, 'auto_dingguo_after',
            ccd_temp_avg, absolute_liquid_avg, guowei_avg,
            data_count=data_count
        )

    def save_before_yinjing_data(self, device_id, furnace_id, ccd_temp,
                                absolute_liquid, guowei_value, target_temp):
        """
        保存引晶前的数据

        参数:
            device_id: 设备ID
            furnace_id: 炉次ID
            ccd_temp: 引晶前CCD温度
            absolute_liquid: 引晶前绝对液口距
            guowei_value: 引晶前埚位值
            target_temp: 引晶前目标温度
        """
        return self.history_manager.save_data(
            device_id, furnace_id, 'before_yinjing',
            ccd_temp, absolute_liquid, guowei_value, target_temp
        )

    def mark_manual_adjustment(self, device_id):
        """
        标记人工调整过埚位

        参数:
            device_id: 设备ID
        """
        return self.history_manager.update_status(
            device_id, manual_guowei_adjusted=True
        )

    def calculate_optimized_correction(self, device_id, current_auto_ccd, target_temp):
        """
        计算优化的液口距校准修正量

        参数:
            device_id: 设备ID
            current_auto_ccd: 本次自动定埚位完成后的CCD温度
            target_temp: 本次引晶前目标温度

        返回:
            dict: 包含修正量和计算详情的字典
        """
        try:
            # 检查设备状态
            status = self.history_manager.get_status(device_id)
            if status and status.get('manual_guowei_adjusted', False):
                return {
                    'correction': 0.0,
                    'method': 'skip_manual_adjusted',
                    'message': '检测到人工调整过埚位，跳过模型调用',
                    'success': False
                }

            # 获取上一炉数据
            last_data = self.history_manager.get_last_furnace_data(device_id)

            if not last_data:
                return {
                    'correction': 0.0,
                    'method': 'no_history',
                    'message': '无上一炉数值信息，建议使用传统模型',
                    'success': False
                }

            # 检查数据完整性
            auto_data = last_data.get('auto_dingguo_after')
            yinjing_data = last_data.get('before_yinjing')

            if not auto_data or not yinjing_data:
                return {
                    'correction': 0.0,
                    'method': 'incomplete_data',
                    'message': '上一炉数据不完整',
                    'success': False
                }

            # 计算上次温度偏差和液口距偏差
            delta_t_last = yinjing_data['target_temp'] - auto_data['ccd_temp']
            delta_l_last = yinjing_data['absolute_liquid'] - auto_data['absolute_liquid']

            # 计算比例系数
            r = self.temp_liquid_model.calculate_ratio(delta_t_last, delta_l_last)

            # 计算本次温度偏差
            delta_t_current = target_temp - current_auto_ccd

            # 计算修正量
            correction = self.temp_liquid_model.predict_correction(r, delta_t_current)

            return {
                'correction': round(correction, 2),
                'method': 'optimized_calculation',
                'details': {
                    'last_delta_t': round(delta_t_last, 2),
                    'last_delta_l': round(delta_l_last, 2),
                    'ratio_r': round(r, 4),
                    'current_delta_t': round(delta_t_current, 2),
                    'current_auto_ccd': current_auto_ccd,
                    'target_temp': target_temp
                },
                'success': True
            }

        except Exception as e:
            logging.error(f"优化计算失败: {e}")
            return {
                'correction': 0.0,
                'method': 'calculation_error',
                'message': f'计算失败: {str(e)}',
                'success': False
            }

    def predict(self, x):
        """
        进行液口距校准预测（兼容传统接口）

        参数:
            x: 包含特征值的字典，支持三种模式：
               1. 优化模式（完整数据）：包含历史数据和当前数据
               2. 优化模式（使用历史库）：包含 device_id, current_auto_ccd, target_temp
               3. 传统模式：包含传统特征参数

        优化模式完整数据参数:
            # 上一炉数据
            last_auto_ccd: 上次自动定埚位完成后CCD温度
            last_auto_liquid: 上次自动定埚位完成后绝对液口距
            last_yinjing_target: 上次引晶前目标温度
            last_yinjing_liquid: 上次引晶前绝对液口距
            # 本次数据
            current_auto_ccd: 本次自动定埚位完成后CCD温度
            target_temp: 本次引晶前目标温度
            # 控制参数
            manual_adjusted: 是否人工调整过埚位 (可选，默认False)

        返回:
            预测的液口距调整值 (float)
        """
        try:
            # 模式1：优化模式（完整数据直接传入）
            if self._has_complete_optimization_data(x):
                return self._calculate_with_complete_data(x)

            # 模式2：优化模式（使用历史数据库）
            device_id = x.get('device_id')
            current_auto_ccd = x.get('current_auto_ccd')
            target_temp = x.get('target_temp')

            if device_id and current_auto_ccd is not None and target_temp is not None:
                result = self.calculate_optimized_correction(device_id, current_auto_ccd, target_temp)
                if result['success']:
                    logging.info(f"使用优化算法(历史库): {result['method']}, 修正量: {result['correction']}mm")
                    return result['correction']
                else:
                    logging.warning(f"优化算法失败，回退到传统模型: {result['message']}")

            # 模式3：传统模型回退
            return self._predict_with_traditional_model(x)

        except Exception as e:
            logging.error(f"预测失败: {e}")
            return 0.0

    def _has_complete_optimization_data(self, x):
        """检查是否包含完整的优化数据"""
        required_keys = [
            'last_auto_ccd', 'last_auto_liquid', 'last_yinjing_target', 'last_yinjing_liquid',
            'current_auto_ccd', 'target_temp'
        ]
        return all(key in x and x[key] is not None for key in required_keys)

    def _calculate_with_complete_data(self, x):
        """使用完整数据进行优化计算"""
        try:
            # 检查是否人工调整过埚位
            if x.get('manual_adjusted', False):
                logging.info("检测到人工调整过埚位，跳过优化算法")
                return self._predict_with_traditional_model(x)

            # 提取数据
            last_auto_ccd = x['last_auto_ccd']
            last_auto_liquid = x['last_auto_liquid']
            last_yinjing_target = x['last_yinjing_target']
            last_yinjing_liquid = x['last_yinjing_liquid']
            current_auto_ccd = x['current_auto_ccd']
            target_temp = x['target_temp']

            # 计算上次温度偏差和液口距偏差
            delta_t_last = last_yinjing_target - last_auto_ccd
            delta_l_last = last_yinjing_liquid - last_auto_liquid

            # 计算比例系数
            r = self.temp_liquid_model.calculate_ratio(delta_t_last, delta_l_last)

            # 计算本次温度偏差
            delta_t_current = target_temp - current_auto_ccd

            # 计算修正量
            correction = self.temp_liquid_model.predict_correction(r, delta_t_current)

            logging.info(f"优化计算完成: 上次ΔT={delta_t_last:.2f}°C, ΔL={delta_l_last:.2f}mm, "
                        f"比例系数r={r:.4f}, 本次ΔT={delta_t_current:.2f}°C, 修正量={correction:.2f}mm")

            return round(correction, 2)

        except Exception as e:
            logging.error(f"完整数据计算失败: {e}")
            return self._predict_with_traditional_model(x)

    def _predict_with_traditional_model(self, x):
        """使用传统模型进行预测"""
        try:
            if self.traditional_model is None:
                logging.warning("传统模型不可用，返回默认值0")
                return 0.0

            # 检查传统模型所需的特征是否完整
            missing_features = [f for f in FEATURE if f not in x or x[f] is None]
            if missing_features:
                logging.warning(f"传统模型缺少特征: {missing_features}，返回默认值0")
                return 0.0

            # 按照指定顺序提取特征
            x_columns = list(map(lambda v: v, FEATURE))
            x_values = list(map(lambda f: x[f], x_columns))

            # 进行预测
            prediction = self.traditional_model.predict([x_values])[0]
            logging.info(f"使用传统模型预测: {prediction}")

            return prediction

        except Exception as e:
            logging.error(f"传统模型预测失败: {e}")
            return 0.0
