"""
液口距校准模型 v3版本 - 优化版本
基于温度偏差的液口距校准修正量计算
简化版本：参考v1结构，所有数据通过call.py传入predict方法
创建时间: 2025-07-28
版本: v3
"""

from joblib import load
import os
import numpy as np

# 传统模型需要的特征（用于回退）
FEATURE = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left', 'daoliutong_right',
           'daoliutong_upleft','daoliutong_upright', 'dingguo_finish_yewen',
           'dingguo_finish_guowei','dingguo_finish_yekouju']

class YekoujuAdjustModel:
    """
    液口距校准模型 v3版本 - 优化版本

    特点:
    - 基于温度偏差的液口距校准修正量计算
    - 简化设计，所有数据通过call.py传入predict方法
    - 向后兼容传统模型预测
    """

    def __init__(self, model_path=None):
        # 加载传统模型（用于回退）
        if model_path is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, 'models', 'retrained_model_v2.joblib')
            model_path = os.path.normpath(model_path)

        print(f"液口距校准模型 v3版本初始化")
        print(f"模型路径: {model_path}")
        print(f"版本: v3 (优化版本)")
        print(f"创建时间: 2025-07-28")

        try:
            self.traditional_model = load(model_path)
            print(f"✅ 传统模型加载成功（用于回退）")
        except Exception as e:
            print(f"⚠️ 传统模型加载失败，将仅使用优化算法: {e}")
            self.traditional_model = None

    def predict(self, x, optimization_params=None):
        """
        进行液口距校准预测

        参数:
            x (dict): 传统模型参数字典，包含：
                daoliutong_up, daoliutong_down, daoliutong_left, daoliutong_right,
                daoliutong_upleft, daoliutong_upright, dingguo_finish_yewen,
                dingguo_finish_guowei, dingguo_finish_yekouju

            optimization_params (dict, optional): 优化算法参数字典，包含：
                last_auto_ccd: 上次自动定埚位完成后CCD温度平均值
                last_auto_liquid: 上次自动定埚位完成后绝对液口距平均值
                last_yinjing_target: 上次引晶前目标温度
                last_yinjing_liquid: 上次引晶前绝对液口距
                current_auto_ccd: 本次自动定埚位完成后CCD温度平均值
                target_temp: 本次引晶前目标温度

        返回:
            预测的液口距调整值 (float)
        """
        try:
            # 检查是否使用优化算法
            if optimization_params and self._has_optimization_data(optimization_params):
                return self._calculate_optimized_correction(optimization_params)
            else:
                # 回退到传统模型
                return self._predict_with_traditional_model(x)

        except Exception as e:
            print(f"预测失败: {e}")
            return 0.0

    def _has_optimization_data(self, optimization_params):
        """检查是否包含优化算法所需的完整数据"""
        if not optimization_params:
            return False

        required_keys = [
            'last_auto_ccd', 'last_auto_liquid', 'last_yinjing_target', 'last_yinjing_liquid',
            'current_auto_ccd', 'target_temp'
        ]
        return all(key in optimization_params and optimization_params[key] is not None for key in required_keys)

    def _calculate_optimized_correction(self, optimization_params):
        """使用优化算法计算液口距校准修正量"""
        try:
            # 提取数据
            last_auto_ccd = optimization_params['last_auto_ccd']
            last_auto_liquid = optimization_params['last_auto_liquid']
            last_yinjing_target = optimization_params['last_yinjing_target']
            last_yinjing_liquid = optimization_params['last_yinjing_liquid']
            current_auto_ccd = optimization_params['current_auto_ccd']
            target_temp = optimization_params['target_temp']

            # 计算上次温度偏差和液口距偏差
            delta_t_last = last_yinjing_target - last_auto_ccd
            delta_l_last = last_yinjing_liquid - last_auto_liquid

            # 计算比例系数 r = ΔL_last / ΔT_last
            if abs(delta_t_last) < 0.1:  # 温度变化太小，使用默认值
                r = 0.1
                print(f"上次温度变化太小({delta_t_last:.2f}°C)，使用默认比例系数: {r}")
            else:
                r = delta_l_last / delta_t_last
                # 限制比例系数的合理范围
                r = np.clip(r, -1.0, 1.0)

            # 计算本次温度偏差
            delta_t_current = target_temp - current_auto_ccd

            # 计算液口距修正量 ΔL_correction = r × ΔT_current
            liquid_correction = r * delta_t_current

            # 转换为埚位调整量（埚位与液口距方向相反）
            guowei_adjust = -liquid_correction

            # 限制埚位调整量的合理范围
            guowei_adjust = np.clip(guowei_adjust, -5.0, 5.0)

            print(f"✅ 优化算法计算: 上次ΔT={delta_t_last:.2f}°C, ΔL={delta_l_last:.2f}mm, "
                  f"比例系数r={r:.4f}, 本次ΔT={delta_t_current:.2f}°C, 液口距修正量={liquid_correction:.2f}mm, "
                  f"埚位调整量={guowei_adjust:.2f}mm")

            return round(guowei_adjust, 2)

        except Exception as e:
            print(f"优化算法计算失败: {e}")
            return 0.0

    def _predict_with_traditional_model(self, x):
        """使用传统模型进行预测"""
        try:
            if self.traditional_model is None:
                print("传统模型不可用，返回默认值0")
                return 0.0

            # 检查传统模型所需的特征是否完整
            missing_features = [f for f in FEATURE if f not in x or x[f] is None]
            if missing_features:
                print(f"传统模型缺少特征: {missing_features}，返回默认值0")
                return 0.0

            # 按照指定顺序提取特征（参考v1版本的实现）
            x_columns = list(map(lambda v: v, FEATURE))
            x_values = list(map(lambda f: x[f], x_columns))

            # 进行预测
            prediction = self.traditional_model.predict([x_values])[0]
            print(f"✅ 使用传统模型预测: {prediction}")

            return prediction

        except Exception as e:
            print(f"传统模型预测失败: {e}")
            return 0.0
