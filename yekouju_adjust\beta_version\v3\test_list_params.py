"""
测试液口距校准v3版本的列表格式参数和埚位修正功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel

def test_list_format_with_guowei_correction():
    """测试列表格式参数和埚位修正功能"""
    print("=" * 80)
    print("测试列表格式参数和埚位修正功能")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    # 传统参数（用于回退）
    x = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 优化算法参数（修正后的格式）
    optimization_params = {
        # 上一炉数据（列表格式，需计算平均值）
        "last_auto_ccd": [1449.5, 1450.0, 1450.5],  # 平均值: 1450.0
        "last_auto_yekouju": [24.8, 25.0, 25.2],    # 平均值: 25.0
        "last_auto_guowei": 100.0,                   # 单个数值
        "last_yinjing_ccd": [1447.5, 1448.0, 1448.5],  # 平均值: 1448.0（引晶前CCD温度）
        "last_yinjing_yekouju": [25.8, 26.0, 26.2], # 平均值: 26.0

        # 上一炉引晶前埚位值（单个数值）
        "last_yinjing_guowei": 101.0,  # 埚位差值: 101.0 - 100.0 = 1.0

        # 本次数据
        "current_auto_ccd": [1451.5, 1452.0, 1452.5]  # 平均值: 1452.0
        # current_yinjing_target 使用固定值1449.75，不再传入
    }
    
    print("📋 输入数据:")
    print("上一炉数据（列表格式）:")
    for key in ['last_auto_ccd', 'last_auto_yekouju', 'last_yinjing_ccd', 'last_yinjing_yekouju']:
        print(f"  {key}: {optimization_params[key]}")

    print("上一炉数据（单个数值）:")
    print(f"  last_auto_guowei: {optimization_params['last_auto_guowei']}")
    print(f"  last_yinjing_guowei: {optimization_params['last_yinjing_guowei']}")

    print("本次数据:")
    print(f"  current_auto_ccd: {optimization_params['current_auto_ccd']}")
    print(f"  current_yinjing_target: {model.YINJING_TARGET_TEMP} (固定值)")
    
    # 进行预测
    result = model.predict(x, optimization_params)
    
    print(f"\n🎯 最终结果: {result}mm")
    
    # 手动验证计算过程
    print("\n🔍 手动验证:")
    last_auto_ccd_avg = sum(optimization_params['last_auto_ccd']) / len(optimization_params['last_auto_ccd'])
    last_auto_yekouju_avg = sum(optimization_params['last_auto_yekouju']) / len(optimization_params['last_auto_yekouju'])
    last_auto_guowei = optimization_params['last_auto_guowei']
    last_yinjing_ccd_avg = sum(optimization_params['last_yinjing_ccd']) / len(optimization_params['last_yinjing_ccd'])
    last_yinjing_yekouju_avg = sum(optimization_params['last_yinjing_yekouju']) / len(optimization_params['last_yinjing_yekouju'])
    current_auto_ccd_avg = sum(optimization_params['current_auto_ccd']) / len(optimization_params['current_auto_ccd'])

    guowei_diff = optimization_params['last_yinjing_guowei'] - last_auto_guowei
    adjusted_last_yinjing_yekouju = last_yinjing_yekouju_avg + guowei_diff

    delta_t_last = last_yinjing_ccd_avg - last_auto_ccd_avg
    delta_l_last = adjusted_last_yinjing_yekouju - last_auto_yekouju_avg
    r = delta_l_last / delta_t_last

    delta_t_current = model.YINJING_TARGET_TEMP - current_auto_ccd_avg
    liquid_correction = r * delta_t_current
    expected_guowei_adjust = -liquid_correction

    print(f"  平均值计算:")
    print(f"    last_auto_ccd_avg: {last_auto_ccd_avg:.2f}")
    print(f"    last_auto_yekouju_avg: {last_auto_yekouju_avg:.2f}")
    print(f"    last_auto_guowei: {last_auto_guowei:.2f}")
    print(f"    last_yinjing_ccd_avg: {last_yinjing_ccd_avg:.2f}")
    print(f"    last_yinjing_yekouju_avg: {last_yinjing_yekouju_avg:.2f}")
    print(f"    current_auto_ccd_avg: {current_auto_ccd_avg:.2f}")

    print(f"  埚位修正:")
    print(f"    埚位差值: {optimization_params['last_yinjing_guowei']} - {last_auto_guowei:.2f} = {guowei_diff:.2f}")
    print(f"    修正后液口距: {last_yinjing_yekouju_avg:.2f} + {guowei_diff:.2f} = {adjusted_last_yinjing_yekouju:.2f}")

    print(f"  比例系数计算:")
    print(f"    delta_t_last: {last_yinjing_ccd_avg:.2f} - {last_auto_ccd_avg:.2f} = {delta_t_last:.2f}")
    print(f"    delta_l_last: {adjusted_last_yinjing_yekouju:.2f} - {last_auto_yekouju_avg:.2f} = {delta_l_last:.2f}")
    print(f"    r: {delta_l_last:.2f} / {delta_t_last:.2f} = {r:.4f}")

    print(f"  最终计算:")
    print(f"    delta_t_current: {model.YINJING_TARGET_TEMP} - {current_auto_ccd_avg:.2f} = {delta_t_current:.2f}")
    print(f"    liquid_correction: {r:.4f} × {delta_t_current:.2f} = {liquid_correction:.2f}")
    print(f"    expected_guowei_adjust: -{liquid_correction:.2f} = {expected_guowei_adjust:.2f}")

    print(f"\n✅ 验证结果: 预期 {expected_guowei_adjust:.2f}mm, 实际 {result}mm")
    print(f"计算正确: {'✅' if abs(result - expected_guowei_adjust) < 0.01 else '❌'}")

def test_invalid_list_data():
    """测试无效列表数据的处理"""
    print("\n" + "=" * 80)
    print("测试无效列表数据的处理")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    x = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 测试空列表
    optimization_params_empty = {
        "last_auto_ccd": [],  # 空列表
        "last_auto_yekouju": [25.0],
        "last_auto_guowei": 100.0,
        "last_yinjing_ccd": [1448.0],
        "last_yinjing_yekouju": [26.0],
        "last_yinjing_guowei": 101.0,
        "current_auto_ccd": [1452.0]
    }
    
    print("测试空列表处理:")
    try:
        result = model.predict(x, optimization_params_empty)
        print(f"结果: {result}mm")
    except Exception as e:
        print(f"✅ 正确捕获异常: {e}")
    
    # 测试包含NaN的列表
    optimization_params_nan = {
        "last_auto_ccd": [1450.0, float('nan'), 1450.5],  # 包含NaN
        "last_auto_yekouju": [25.0],
        "last_auto_guowei": 100.0,
        "last_yinjing_ccd": [1448.0],
        "last_yinjing_yekouju": [26.0],
        "last_yinjing_guowei": 101.0,
        "current_auto_ccd": [1452.0]
    }
    
    print("\n测试包含NaN的列表处理:")
    try:
        result = model.predict(x, optimization_params_nan)
        print(f"结果: {result}mm")
        print("✅ 正确处理了NaN值")
    except Exception as e:
        print(f"异常: {e}")

def test_traditional_fallback():
    """测试传统模式回退"""
    print("\n" + "=" * 80)
    print("测试传统模式回退（参数不完整）")
    print("=" * 80)
    
    model = YekoujuAdjustModel()
    
    x = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 缺少部分参数
    incomplete_params = {
        "last_auto_ccd": [1450.0],
        "last_auto_yekouju": [25.0],
        # 缺少其他参数
    }
    
    result = model.predict(x, incomplete_params)
    print(f"不完整参数结果: {result}mm")
    print(f"正确回退到传统模型: {'✅' if result == 0.0 else '❌'}")

if __name__ == "__main__":
    print("液口距校准v3版本 - 列表格式参数测试")
    print("=" * 80)
    
    try:
        test_list_format_with_guowei_correction()
        test_invalid_list_data()
        test_traditional_fallback()
        
        print("\n" + "=" * 80)
        print("✅ 列表格式参数测试完成")
        print("💡 新功能特点：")
        print("   - 支持列表格式参数，自动计算平均值")
        print("   - 新增埚位修正功能，提高计算精度")
        print("   - 完善的异常处理和数据验证")
        print("   - 保持传统模型回退机制")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
