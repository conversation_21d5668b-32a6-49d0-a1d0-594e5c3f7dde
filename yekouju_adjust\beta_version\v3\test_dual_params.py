"""
测试v3版本双字典参数接口
验证传统模式参数字典和优化方案参数字典的独立传入
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel

def test_optimization_mode():
    """测试优化模式（双字典参数）"""
    print("=== 测试优化模式（双字典参数） ===")
    
    model = YekoujuAdjustModel()
    
    # 传统模式参数字典
    traditional_params = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 优化方案参数字典
    optimization_params = {
        "last_auto_ccd": 1450.0,      # 上次自动定埚位完成后CCD温度
        "last_auto_liquid": 25.0,     # 上次自动定埚位完成后绝对液口距
        "last_yinjing_target": 1448.0, # 上次引晶前目标温度
        "last_yinjing_liquid": 26.0,  # 上次引晶前绝对液口距
        "current_auto_ccd": 1452.0,   # 本次自动定埚位完成后CCD温度
        "target_temp": 1449.0,        # 本次引晶前目标温度
        "manual_adjusted": False      # 未人工调整
    }
    
    result = model.predict(traditional_params, optimization_params)
    print(f"优化模式预测结果: {result}mm")
    
    # 手动验证计算过程
    delta_t_last = optimization_params["last_yinjing_target"] - optimization_params["last_auto_ccd"]  # -2.0
    delta_l_last = optimization_params["last_yinjing_liquid"] - optimization_params["last_auto_liquid"]  # 1.0
    r = delta_l_last / delta_t_last  # -0.5
    delta_t_current = optimization_params["target_temp"] - optimization_params["current_auto_ccd"]  # -3.0
    expected_correction = r * delta_t_current  # 1.5
    
    print(f"计算验证:")
    print(f"  上次温度偏差: {delta_t_last}°C")
    print(f"  上次液口距变化: {delta_l_last}mm")
    print(f"  比例系数: {r} mm/°C")
    print(f"  本次温度偏差: {delta_t_current}°C")
    print(f"  预期修正量: {expected_correction}mm")
    print(f"  实际结果: {result}mm")
    print(f"  结果匹配: {'✅' if abs(result - expected_correction) < 0.01 else '❌'}")

def test_traditional_mode_only():
    """测试纯传统模式（只传入传统参数字典）"""
    print("\n=== 测试纯传统模式 ===")
    
    model = YekoujuAdjustModel()
    
    # 只有传统模式参数字典
    traditional_params = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 不传入优化参数字典
    result = model.predict(traditional_params)
    print(f"纯传统模式预测结果: {result}mm")
    print(f"使用传统模型: {'✅' if result == 0.0 else '❌'}")  # 因为没有加载sklearn模型

def test_manual_adjusted():
    """测试人工调整模式"""
    print("\n=== 测试人工调整模式 ===")
    
    model = YekoujuAdjustModel()
    
    traditional_params = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    optimization_params = {
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1448.0,
        "last_yinjing_liquid": 26.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": True  # 人工调整过
    }
    
    result = model.predict(traditional_params, optimization_params)
    print(f"人工调整模式预测结果: {result}mm")
    print(f"应该跳过优化算法: {'✅' if result == 0.0 else '❌'}")

def test_incomplete_optimization_params():
    """测试优化参数不完整的情况"""
    print("\n=== 测试优化参数不完整情况 ===")
    
    model = YekoujuAdjustModel()
    
    traditional_params = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 缺少部分优化参数
    incomplete_optimization_params = {
        "last_auto_ccd": 1450.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        # 缺少 last_auto_liquid, last_yinjing_target, last_yinjing_liquid
    }
    
    result = model.predict(traditional_params, incomplete_optimization_params)
    print(f"优化参数不完整预测结果: {result}mm")
    print(f"应该回退到传统模型: {'✅' if result == 0.0 else '❌'}")

def test_empty_optimization_params():
    """测试空的优化参数字典"""
    print("\n=== 测试空的优化参数字典 ===")
    
    model = YekoujuAdjustModel()
    
    traditional_params = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    # 空的优化参数字典
    empty_optimization_params = {}
    
    result = model.predict(traditional_params, empty_optimization_params)
    print(f"空优化参数字典预测结果: {result}mm")
    print(f"应该回退到传统模型: {'✅' if result == 0.0 else '❌'}")

def test_api_simulation():
    """模拟API调用（双字典参数）"""
    print("\n=== 模拟API调用（双字典参数） ===")
    
    # 模拟request.json数据
    class MockRequest:
        def __init__(self, json_data):
            self.json = json_data
            self.method = 'POST'
    
    # 完整数据API调用
    api_data = {
        # 优化算法参数
        "last_auto_ccd": 1450.0,
        "last_auto_liquid": 25.0,
        "last_yinjing_target": 1448.0,
        "last_yinjing_liquid": 26.0,
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        "manual_adjusted": False,
        
        # 传统参数
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0,
        "recent_absolute_liquid": [25.1, 25.0, 24.9, 25.2, 25.0]
    }
    
    # 模拟call.py中的数据处理
    from yekouju_adjust.beta_version.v3.call import filter_absolute_liquid
    
    # 滤波处理
    absolute_liquid_filter = filter_absolute_liquid(api_data["recent_absolute_liquid"])
    if absolute_liquid_filter is not None:
        api_data["dingguo_finish_yekouju"] = absolute_liquid_filter
    
    # 构建传统参数字典
    traditional_params = {
        "daoliutong_up": api_data["daoliutong_up"],
        "daoliutong_down": api_data["daoliutong_down"],
        "daoliutong_left": api_data["daoliutong_left"],
        "daoliutong_right": api_data["daoliutong_right"],
        "daoliutong_upleft": api_data["daoliutong_upleft"],
        "daoliutong_upright": api_data["daoliutong_upright"],
        "dingguo_finish_yewen": api_data["dingguo_finish_yewen"],
        "dingguo_finish_guowei": api_data["dingguo_finish_guowei"],
        "dingguo_finish_yekouju": api_data["dingguo_finish_yekouju"]
    }
    
    # 构建优化参数字典
    optimization_params = {
        "last_auto_ccd": api_data["last_auto_ccd"],
        "last_auto_liquid": api_data["last_auto_liquid"],
        "last_yinjing_target": api_data["last_yinjing_target"],
        "last_yinjing_liquid": api_data["last_yinjing_liquid"],
        "current_auto_ccd": api_data["current_auto_ccd"],
        "target_temp": api_data["target_temp"],
        "manual_adjusted": api_data["manual_adjusted"]
    }
    
    model = YekoujuAdjustModel()
    result = model.predict(traditional_params, optimization_params)
    print(f"API模拟调用结果: {result}mm")
    print(f"滤波后的液口距: {absolute_liquid_filter}")
    print(f"传统参数字典: {len(traditional_params)} 个参数")
    print(f"优化参数字典: {len(optimization_params)} 个参数")

if __name__ == "__main__":
    print("液口距校准v3版本双字典参数接口测试")
    print("=" * 60)
    
    try:
        test_optimization_mode()
        test_traditional_mode_only()
        test_manual_adjusted()
        test_incomplete_optimization_params()
        test_empty_optimization_params()
        test_api_simulation()
        
        print("\n" + "=" * 60)
        print("✅ 双字典参数接口测试完成")
        print("💡 核心特点：")
        print("   - 传统模式参数字典：与v1/v2版本完全一致")
        print("   - 优化方案参数字典：独立的优化算法参数")
        print("   - 自动回退机制：优化参数不完整时使用传统模型")
        print("   - 清晰的参数分离：两个字典职责明确")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
