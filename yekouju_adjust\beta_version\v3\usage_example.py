"""
液口距校准优化模型使用示例
展示如何在实际生产中使用新的优化算法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from yekouju_adjust.beta_version.v3.model import YekoujuAdjustModel
import json

def example_production_workflow():
    """生产环境使用示例"""
    print("液口距校准优化算法 - 生产使用示例")
    print("=" * 60)
    
    # 初始化模型
    model = YekoujuAdjustModel()
    
    device_id = "A001"  # 设备编号
    
    print(f"设备: {device_id}")
    print("\n📋 生产流程演示:")
    
    # === 第一炉：建立基准数据 ===
    print("\n🔥 第一炉 (F20250728001):")
    furnace1 = "F20250728001"
    
    print("1️⃣ 自动定埚位完成后 (10-12分钟平均值):")
    # 模拟采集10-12分钟的数据
    ccd_temps = [1450.2, 1450.1, 1449.9, 1450.0, 1450.3, 1450.1, 1449.8, 1450.2, 1450.0, 1450.1]
    liquid_values = [25.1, 25.0, 24.9, 25.2, 25.0, 25.1, 24.8, 25.0, 25.1, 25.0]
    guowei_values = [100.1, 100.0, 99.9, 100.2, 100.0, 100.1, 99.8, 100.0, 100.1, 100.0]
    
    ccd_avg = sum(ccd_temps) / len(ccd_temps)
    liquid_avg = sum(liquid_values) / len(liquid_values)
    guowei_avg = sum(guowei_values) / len(guowei_values)
    
    print(f"   CCD温度平均值: {ccd_avg:.1f}°C")
    print(f"   绝对液口距平均值: {liquid_avg:.1f}mm")
    print(f"   埚位值平均值: {guowei_avg:.1f}mm")
    
    # 保存数据
    model.save_auto_dingguo_data(device_id, furnace1, ccd_avg, liquid_avg, guowei_avg, len(ccd_temps))
    print("   ✅ 数据已保存")
    
    print("\n2️⃣ 引晶前:")
    yinjing_ccd = 1448.0
    yinjing_liquid = 26.0
    yinjing_guowei = 101.0
    target_temp = 1448.0
    
    print(f"   CCD温度: {yinjing_ccd}°C")
    print(f"   绝对液口距: {yinjing_liquid}mm")
    print(f"   埚位值: {yinjing_guowei}mm")
    print(f"   目标温度: {target_temp}°C")
    
    # 保存引晶前数据
    model.save_before_yinjing_data(device_id, furnace1, yinjing_ccd, yinjing_liquid, yinjing_guowei, target_temp)
    print("   ✅ 数据已保存")
    
    # === 第二炉：使用优化算法 ===
    print("\n🔥 第二炉 (F20250728002):")
    furnace2 = "F20250728002"
    
    print("1️⃣ 自动定埚位完成后:")
    current_auto_ccd = 1452.0
    print(f"   CCD温度: {current_auto_ccd}°C")
    
    print("2️⃣ 引晶前准备:")
    current_target = 1449.0
    print(f"   目标温度: {current_target}°C")
    
    print("3️⃣ 🧠 智能计算液口距修正量:")
    result = model.calculate_optimized_correction(device_id, current_auto_ccd, current_target)
    
    if result['success']:
        correction = result['correction']
        details = result['details']
        
        print(f"   📊 计算详情:")
        print(f"      上次温度偏差: {details['last_delta_t']}°C")
        print(f"      上次液口距变化: {details['last_delta_l']}mm")
        print(f"      学习到的比例系数: {details['ratio_r']} mm/°C")
        print(f"      本次温度偏差: {details['current_delta_t']}°C")
        print(f"   🎯 建议修正量: {correction}mm")
        
        if abs(correction) > 0.1:
            if correction > 0:
                print(f"   💡 操作建议: 埚位下降 {abs(correction)}mm (增加液口距)")
            else:
                print(f"   💡 操作建议: 埚位上升 {abs(correction)}mm (减少液口距)")
        else:
            print(f"   💡 操作建议: 无需调整")
    else:
        print(f"   ⚠️ 计算失败: {result['message']}")
    
    # === 异常情况处理 ===
    print("\n🚨 异常情况处理演示:")
    
    print("\n场景1: 人工调整过埚位")
    model.mark_manual_adjustment(device_id)
    result_manual = model.calculate_optimized_correction(device_id, 1451.0, 1448.0)
    print(f"   结果: {result_manual['message']}")
    
    print("\n场景2: 新设备无历史数据")
    new_device = "B002"
    result_new = model.calculate_optimized_correction(new_device, 1450.0, 1448.0)
    print(f"   结果: {result_new['message']}")
    
    # === 传统模型回退演示 ===
    print("\n🔄 传统模型回退演示:")
    traditional_data = {
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0
    }
    
    traditional_result = model.predict(traditional_data)
    print(f"   传统模型预测结果: {traditional_result}mm")

def example_api_usage():
    """API接口使用示例"""
    print("\n" + "=" * 60)
    print("📡 API接口使用示例")
    print("=" * 60)
    
    print("\n1. 保存自动定埚位数据 API:")
    print("POST /save_auto_dingguo_data")
    api_data1 = {
        "device_id": "A001",
        "furnace_id": "F20250728001",
        "ccd_temp_list": [1450.2, 1450.1, 1449.9, 1450.0, 1450.3],
        "absolute_liquid_list": [25.1, 25.0, 24.9, 25.2, 25.0],
        "guowei_list": [100.1, 100.0, 99.9, 100.2, 100.0]
    }
    print(json.dumps(api_data1, indent=2, ensure_ascii=False))
    
    print("\n2. 保存引晶前数据 API:")
    print("POST /save_before_yinjing_data")
    api_data2 = {
        "device_id": "A001",
        "furnace_id": "F20250728001",
        "ccd_temp": 1448.0,
        "absolute_liquid": 26.0,
        "guowei_value": 101.0,
        "target_temp": 1448.0
    }
    print(json.dumps(api_data2, indent=2, ensure_ascii=False))
    
    print("\n3. 优化液口距校准 API:")
    print("POST /yekouju_adjust_test (v3版本)")
    api_data3 = {
        "device_id": "A001",
        "current_auto_ccd": 1452.0,
        "target_temp": 1449.0,
        # 传统参数（用于回退）
        "daoliutong_up": 1.0,
        "daoliutong_down": 1.0,
        "daoliutong_left": 1.0,
        "daoliutong_right": 1.0,
        "daoliutong_upleft": 1.0,
        "daoliutong_upright": 1.0,
        "dingguo_finish_yewen": 1450.0,
        "dingguo_finish_guowei": 100.0,
        "dingguo_finish_yekouju": 25.0,
        "recent_absolute_liquid": [25.1, 25.0, 24.9, 25.2, 25.0]
    }
    print(json.dumps(api_data3, indent=2, ensure_ascii=False))
    
    print("\n4. 标记人工调整 API:")
    print("POST /mark_manual_adjustment")
    api_data4 = {
        "device_id": "A001"
    }
    print(json.dumps(api_data4, indent=2, ensure_ascii=False))

def example_integration_points():
    """集成要点说明"""
    print("\n" + "=" * 60)
    print("🔧 系统集成要点")
    print("=" * 60)
    
    print("\n📝 数据采集时机:")
    print("1. 自动定埚位完成后10-12分钟:")
    print("   - 每分钟采集CCD温度、绝对液口距、埚位值")
    print("   - 计算平均值并保存")
    print("   - 避免瞬时值波动影响")
    
    print("\n2. 引晶前:")
    print("   - 采集CCD温度、绝对液口距、埚位值")
    print("   - 获取引晶前目标温度")
    print("   - 立即保存数据")
    
    print("\n🎯 调用时机:")
    print("1. 本次自动定埚位完成后:")
    print("   - 获取10-12分钟平均数据")
    print("   - 获取引晶前目标温度")
    print("   - 调用优化算法计算修正量")
    
    print("\n⚠️ 异常处理:")
    print("1. 检测到人工调整埚位 → 跳过模型调用")
    print("2. 无上一炉数据 → 使用传统模型")
    print("3. 数据不完整 → 使用传统模型")
    print("4. 计算异常 → 返回0（不调整）")
    
    print("\n🔄 向后兼容:")
    print("1. 保持原有API接口不变")
    print("2. 新增优化参数为可选")
    print("3. 自动回退到传统模型")

if __name__ == "__main__":
    try:
        example_production_workflow()
        example_api_usage()
        example_integration_points()
        
        print("\n" + "=" * 60)
        print("✅ 示例演示完成！")
        print("💡 优化算法已准备就绪，可以集成到生产系统中。")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
